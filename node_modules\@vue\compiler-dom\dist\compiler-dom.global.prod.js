/**
* @vue/compiler-dom v3.5.0
* (c) 2018-present <PERSON><PERSON> (<PERSON>) <PERSON> and Vue contributors
* @license MIT
**/var VueCompilerDOM=function(e){"use strict";let t;/*! #__NO_SIDE_EFFECTS__ */function n(e,t){let n=new Set(e.split(","));return t?e=>n.has(e.toLowerCase()):e=>n.has(e)}let i={},s=()=>{},r=()=>!1,o=e=>111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&(e.charCodeAt(2)>122||97>e.charCodeAt(2)),a=Object.assign,l=Array.isArray,c=e=>"string"==typeof e,h=e=>"symbol"==typeof e,d=e=>null!==e&&"object"==typeof e,p=/* @__PURE__ */n(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),u=/* @__PURE__ */n("bind,cloak,else-if,else,for,html,if,model,on,once,pre,show,slot,text,memo"),f=e=>{let t=/* @__PURE__ */Object.create(null);return n=>t[n]||(t[n]=e(n))},E=/-(\w)/g,_=f(e=>e.replace(E,(e,t)=>t?t.toUpperCase():"")),m=f(e=>e.charAt(0).toUpperCase()+e.slice(1)),S=f(e=>e?`on${m(e)}`:""),g=/;(?![^(]*\))/g,T=/:([^]+)/,N=/\/\*[^]*?\*\//g,I=/* @__PURE__ */n("html,body,base,head,link,meta,style,title,address,article,aside,footer,header,hgroup,h1,h2,h3,h4,h5,h6,nav,section,div,dd,dl,dt,figcaption,figure,picture,hr,img,li,main,ol,p,pre,ul,a,b,abbr,bdi,bdo,br,cite,code,data,dfn,em,i,kbd,mark,q,rp,rt,ruby,s,samp,small,span,strong,sub,sup,time,u,var,wbr,area,audio,map,track,video,embed,object,param,source,canvas,script,noscript,del,ins,caption,col,colgroup,table,thead,tbody,td,th,tr,button,datalist,fieldset,form,input,label,legend,meter,optgroup,option,output,progress,select,textarea,details,dialog,menu,summary,template,blockquote,iframe,tfoot"),y=/* @__PURE__ */n("svg,animate,animateMotion,animateTransform,circle,clipPath,color-profile,defs,desc,discard,ellipse,feBlend,feColorMatrix,feComponentTransfer,feComposite,feConvolveMatrix,feDiffuseLighting,feDisplacementMap,feDistantLight,feDropShadow,feFlood,feFuncA,feFuncB,feFuncG,feFuncR,feGaussianBlur,feImage,feMerge,feMergeNode,feMorphology,feOffset,fePointLight,feSpecularLighting,feSpotLight,feTile,feTurbulence,filter,foreignObject,g,hatch,hatchpath,image,line,linearGradient,marker,mask,mesh,meshgradient,meshpatch,meshrow,metadata,mpath,path,pattern,polygon,polyline,radialGradient,rect,set,solidcolor,stop,switch,symbol,text,textPath,title,tspan,unknown,use,view"),O=/* @__PURE__ */n("annotation,annotation-xml,maction,maligngroup,malignmark,math,menclose,merror,mfenced,mfrac,mfraction,mglyph,mi,mlabeledtr,mlongdiv,mmultiscripts,mn,mo,mover,mpadded,mphantom,mprescripts,mroot,mrow,ms,mscarries,mscarry,msgroup,msline,mspace,msqrt,msrow,mstack,mstyle,msub,msubsup,msup,mtable,mtd,mtext,mtr,munder,munderover,none,semantics"),A=/* @__PURE__ */n("area,base,br,col,embed,hr,img,input,link,meta,param,source,track,wbr"),C=Symbol(""),b=Symbol(""),R=Symbol(""),v=Symbol(""),x=Symbol(""),L=Symbol(""),M=Symbol(""),D=Symbol(""),P=Symbol(""),V=Symbol(""),k=Symbol(""),X=Symbol(""),w=Symbol(""),U=Symbol(""),F=Symbol(""),B=Symbol(""),$=Symbol(""),H=Symbol(""),G=Symbol(""),q=Symbol(""),J=Symbol(""),j=Symbol(""),W=Symbol(""),K=Symbol(""),Y=Symbol(""),Q=Symbol(""),z=Symbol(""),Z=Symbol(""),ee=Symbol(""),et=Symbol(""),en=Symbol(""),ei=Symbol(""),es=Symbol(""),er=Symbol(""),eo=Symbol(""),ea=Symbol(""),el=Symbol(""),ec=Symbol(""),eh=Symbol(""),ed={[C]:"Fragment",[b]:"Teleport",[R]:"Suspense",[v]:"KeepAlive",[x]:"BaseTransition",[L]:"openBlock",[M]:"createBlock",[D]:"createElementBlock",[P]:"createVNode",[V]:"createElementVNode",[k]:"createCommentVNode",[X]:"createTextVNode",[w]:"createStaticVNode",[U]:"resolveComponent",[F]:"resolveDynamicComponent",[B]:"resolveDirective",[$]:"resolveFilter",[H]:"withDirectives",[G]:"renderList",[q]:"renderSlot",[J]:"createSlots",[j]:"toDisplayString",[W]:"mergeProps",[K]:"normalizeClass",[Y]:"normalizeStyle",[Q]:"normalizeProps",[z]:"guardReactiveProps",[Z]:"toHandlers",[ee]:"camelize",[et]:"capitalize",[en]:"toHandlerKey",[ei]:"setBlockTracking",[es]:"pushScopeId",[er]:"popScopeId",[eo]:"withCtx",[ea]:"unref",[el]:"isRef",[ec]:"withMemo",[eh]:"isMemoSame"};function ep(e){Object.getOwnPropertySymbols(e).forEach(t=>{ed[t]=e[t]})}let eu={start:{line:1,column:1,offset:0},end:{line:1,column:1,offset:0},source:""};function ef(e,t=""){return{type:0,source:t,children:e,helpers:/* @__PURE__ */new Set,components:[],directives:[],hoists:[],imports:[],cached:[],temps:0,codegenNode:void 0,loc:eu}}function eE(e,t,n,i,s,r,o,a=!1,l=!1,c=!1,h=eu){return e&&(a?(e.helper(L),e.helper(eb(e.inSSR,c))):e.helper(eC(e.inSSR,c)),o&&e.helper(H)),{type:13,tag:t,props:n,children:i,patchFlag:s,dynamicProps:r,directives:o,isBlock:a,disableTracking:l,isComponent:c,loc:h}}function e_(e,t=eu){return{type:17,loc:t,elements:e}}function em(e,t=eu){return{type:15,loc:t,properties:e}}function eS(e,t){return{type:16,loc:eu,key:c(e)?eg(e,!0):e,value:t}}function eg(e,t=!1,n=eu,i=0){return{type:4,loc:n,content:e,isStatic:t,constType:t?3:i}}function eT(e,t=eu){return{type:8,loc:t,children:e}}function eN(e,t=[],n=eu){return{type:14,loc:n,callee:e,arguments:t}}function eI(e,t,n=!1,i=!1,s=eu){return{type:18,params:e,returns:t,newline:n,isSlot:i,loc:s}}function ey(e,t,n,i=!0){return{type:19,test:e,consequent:t,alternate:n,newline:i,loc:eu}}function eO(e,t,n=!1){return{type:20,index:e,value:t,needPauseTracking:n,needArraySpread:!1,loc:eu}}function eA(e){return{type:21,body:e,loc:eu}}function eC(e,t){return e||t?P:V}function eb(e,t){return e||t?M:D}function eR(e,{helper:t,removeHelper:n,inSSR:i}){e.isBlock||(e.isBlock=!0,n(eC(i,e.isComponent)),t(L),t(eb(i,e.isComponent)))}let ev=new Uint8Array([123,123]),ex=new Uint8Array([125,125]);function eL(e){return e>=97&&e<=122||e>=65&&e<=90}function eM(e){return 32===e||10===e||9===e||12===e||13===e}function eD(e){return 47===e||62===e||eM(e)}function eP(e){let t=new Uint8Array(e.length);for(let n=0;n<e.length;n++)t[n]=e.charCodeAt(n);return t}let eV={Cdata:new Uint8Array([67,68,65,84,65,91]),CdataEnd:new Uint8Array([93,93,62]),CommentEnd:new Uint8Array([45,45,62]),ScriptEnd:new Uint8Array([60,47,115,99,114,105,112,116]),StyleEnd:new Uint8Array([60,47,115,116,121,108,101]),TitleEnd:new Uint8Array([60,47,116,105,116,108,101]),TextareaEnd:new Uint8Array([60,47,116,101,120,116,97,114,101,97])},ek={COMPILER_IS_ON_ELEMENT:{message:'Platform-native elements with "is" prop will no longer be treated as components in Vue 3 unless the "is" value is explicitly prefixed with "vue:".',link:"https://v3-migration.vuejs.org/breaking-changes/custom-elements-interop.html"},COMPILER_V_BIND_SYNC:{message:e=>`.sync modifier for v-bind has been removed. Use v-model with argument instead. \`v-bind:${e}.sync\` should be changed to \`v-model:${e}\`.`,link:"https://v3-migration.vuejs.org/breaking-changes/v-model.html"},COMPILER_V_BIND_OBJECT_ORDER:{message:'v-bind="obj" usage is now order sensitive and behaves like JavaScript object spread: it will now overwrite an existing non-mergeable attribute that appears before v-bind in the case of conflict. To retain 2.x behavior, move v-bind to make it the first attribute. You can also suppress this warning if the usage is intended.',link:"https://v3-migration.vuejs.org/breaking-changes/v-bind.html"},COMPILER_V_ON_NATIVE:{message:".native modifier for v-on has been removed as is no longer necessary.",link:"https://v3-migration.vuejs.org/breaking-changes/v-on-native-modifier-removed.html"},COMPILER_V_IF_V_FOR_PRECEDENCE:{message:"v-if / v-for precedence when used on the same element has changed in Vue 3: v-if now takes higher precedence and will no longer have access to v-for scope variables. It is best to avoid the ambiguity with <template> tags or use a computed property that filters v-for data source.",link:"https://v3-migration.vuejs.org/breaking-changes/v-if-v-for.html"},COMPILER_NATIVE_TEMPLATE:{message:"<template> with no special directives will render as a native template element instead of its inner content in Vue 3."},COMPILER_INLINE_TEMPLATE:{message:'"inline-template" has been removed in Vue 3.',link:"https://v3-migration.vuejs.org/breaking-changes/inline-template-attribute.html"},COMPILER_FILTERS:{message:'filters have been removed in Vue 3. The "|" symbol will be treated as native JavaScript bitwise OR operator. Use method calls or computed properties instead.',link:"https://v3-migration.vuejs.org/breaking-changes/filters.html"}};function eX(e,{compatConfig:t}){let n=t&&t[e];return"MODE"===e?n||3:n}function ew(e,t){let n=eX("MODE",t),i=eX(e,t);return 3===n?!0===i:!1!==i}function eU(e,t,n,...i){return ew(e,t)}function eF(e){throw e}function eB(e){}function /*#__PURE__*/e$(e,t,n,i){let s=SyntaxError(String(`https://vuejs.org/error-reference/#compiler-${e}`));return s.code=e,s.loc=t,s}let eH={0:"Illegal comment.",1:"CDATA section is allowed only in XML context.",2:"Duplicate attribute.",3:"End tag cannot have attributes.",4:"Illegal '/' in tags.",5:"Unexpected EOF in tag.",6:"Unexpected EOF in CDATA section.",7:"Unexpected EOF in comment.",8:"Unexpected EOF in script.",9:"Unexpected EOF in tag.",10:"Incorrectly closed comment.",11:"Incorrectly opened comment.",12:"Illegal tag name. Use '&lt;' to print '<'.",13:"Attribute value was expected.",14:"End tag name was expected.",15:"Whitespace was expected.",16:"Unexpected '<!--' in comment.",17:"Attribute name cannot contain U+0022 (\"), U+0027 ('), and U+003C (<).",18:"Unquoted attribute value cannot contain U+0022 (\"), U+0027 ('), U+003C (<), U+003D (=), and U+0060 (`).",19:"Attribute name cannot start with '='.",21:"'<?' is allowed only in XML context.",20:"Unexpected null character.",22:"Illegal '/' in tags.",23:"Invalid end tag.",24:"Element is missing end tag.",25:"Interpolation end sign was not found.",27:"End bracket for dynamic directive argument was not found. Note that dynamic directive argument cannot contain spaces.",26:"Legal directive name was expected.",28:"v-if/v-else-if is missing expression.",29:"v-if/else branches must use unique keys.",30:"v-else/v-else-if has no adjacent v-if or v-else-if.",31:"v-for is missing expression.",32:"v-for has invalid expression.",33:"<template v-for> key should be placed on the <template> tag.",34:"v-bind is missing expression.",52:"v-bind with same-name shorthand only allows static argument.",35:"v-on is missing expression.",36:"Unexpected custom directive on <slot> outlet.",37:"Mixed v-slot usage on both the component and nested <template>. When there are multiple named slots, all slots should use <template> syntax to avoid scope ambiguity.",38:"Duplicate slot names found. ",39:"Extraneous children found when component already has explicitly named default slot. These children will be ignored.",40:"v-slot can only be used on components or <template> tags.",41:"v-model is missing expression.",42:"v-model value must be a valid JavaScript member expression.",43:"v-model cannot be used on v-for or v-slot scope variables because they are not writable.",44:`v-model cannot be used on a prop, because local prop bindings are not writable.
Use a v-bind binding combined with a v-on listener that emits update:x event instead.`,45:"Error parsing JavaScript expression: ",46:"<KeepAlive> expects exactly one child component.",51:"@vnode-* hooks in templates are no longer supported. Use the vue: prefix instead. For example, @vnode-mounted should be changed to @vue:mounted. @vnode-* hooks support has been removed in 3.4.",47:'"prefixIdentifiers" option is not supported in this build of compiler.',48:"ES module mode is not supported in this build of compiler.",49:'"cacheHandlers" option is only supported when the "prefixIdentifiers" option is enabled.',50:'"scopeId" option is only supported in module mode.',53:""};function eG(e,t=[]){switch(e.type){case"Identifier":t.push(e);break;case"MemberExpression":let n=e;for(;"MemberExpression"===n.type;)n=n.object;t.push(n);break;case"ObjectPattern":for(let n of e.properties)"RestElement"===n.type?eG(n.argument,t):eG(n.value,t);break;case"ArrayPattern":e.elements.forEach(e=>{e&&eG(e,t)});break;case"RestElement":eG(e.argument,t);break;case"AssignmentPattern":eG(e.left,t)}return t}let eq=e=>e&&("ObjectProperty"===e.type||"ObjectMethod"===e.type)&&!e.computed,eJ=["TSAsExpression","TSTypeAssertion","TSNonNullExpression","TSInstantiationExpression","TSSatisfiesExpression"],ej=e=>4===e.type&&e.isStatic;function eW(e){switch(e){case"Teleport":case"teleport":return b;case"Suspense":case"suspense":return R;case"KeepAlive":case"keep-alive":return v;case"BaseTransition":case"base-transition":return x}}let eK=/^\d|[^\$\w\xA0-\uFFFF]/,eY=e=>!eK.test(e),eQ=/[A-Za-z_$\xA0-\uFFFF]/,ez=/[\.\?\w$\xA0-\uFFFF]/,eZ=/\s+[.[]\s*|\s*[.[]\s+/g,e1=e=>4===e.type?e.content:e.loc.source,e0=e=>{let t=e1(e).trim().replace(eZ,e=>e.trim()),n=0,i=[],s=0,r=0,o=null;for(let e=0;e<t.length;e++){let a=t.charAt(e);switch(n){case 0:if("["===a)i.push(n),n=1,s++;else if("("===a)i.push(n),n=2,r++;else if(!(0===e?eQ:ez).test(a))return!1;break;case 1:"'"===a||'"'===a||"`"===a?(i.push(n),n=3,o=a):"["===a?s++:"]"!==a||--s||(n=i.pop());break;case 2:if("'"===a||'"'===a||"`"===a)i.push(n),n=3,o=a;else if("("===a)r++;else if(")"===a){if(e===t.length-1)return!1;--r||(n=i.pop())}break;case 3:a===o&&(n=i.pop(),o=null)}}return!s&&!r},e2=/^\s*(async\s*)?(\([^)]*?\)|[\w$_]+)\s*(:[^=]+)?=>|^\s*(async\s+)?function(?:\s+[\w$]+)?\s*\(/,e3=e=>e2.test(e1(e));function e4(e,t,n=t.length){let i=0,s=-1;for(let e=0;e<n;e++)10===t.charCodeAt(e)&&(i++,s=e);return e.offset+=n,e.line+=i,e.column=-1===s?e.column+n:n-s,e}function e6(e,t,n=!1){for(let i=0;i<e.props.length;i++){let s=e.props[i];if(7===s.type&&(n||s.exp)&&(c(t)?s.name===t:t.test(s.name)))return s}}function e5(e,t,n=!1,i=!1){for(let s=0;s<e.props.length;s++){let r=e.props[s];if(6===r.type){if(n)continue;if(r.name===t&&(r.value||i))return r}else if("bind"===r.name&&(r.exp||i)&&e9(r.arg,t))return r}}function e9(e,t){return!!(e&&ej(e)&&e.content===t)}function e7(e){return e.props.some(e=>7===e.type&&"bind"===e.name&&(!e.arg||4!==e.arg.type||!e.arg.isStatic))}function e8(e){return 5===e.type||2===e.type}function te(e){return 7===e.type&&"slot"===e.name}function tt(e){return 1===e.type&&3===e.tagType}function tn(e){return 1===e.type&&2===e.tagType}let ti=/* @__PURE__ */new Set([Q,z]);function ts(e,t,n){let i,s;let r=13===e.type?e.props:e.arguments[2],o=[];if(r&&!c(r)&&14===r.type){let e=function e(t,n=[]){if(t&&!c(t)&&14===t.type){let i=t.callee;if(!c(i)&&ti.has(i))return e(t.arguments[0],n.concat(t))}return[t,n]}(r);r=e[0],s=(o=e[1])[o.length-1]}if(null==r||c(r))i=em([t]);else if(14===r.type){let e=r.arguments[0];c(e)||15!==e.type?r.callee===Z?i=eN(n.helper(W),[em([t]),r]):r.arguments.unshift(em([t])):tr(t,e)||e.properties.unshift(t),i||(i=r)}else 15===r.type?(tr(t,r)||r.properties.unshift(t),i=r):(i=eN(n.helper(W),[em([t]),r]),s&&s.callee===z&&(s=o[o.length-2]));13===e.type?s?s.arguments[0]=i:e.props=i:s?s.arguments[0]=i:e.arguments[2]=i}function tr(e,t){let n=!1;if(4===e.key.type){let i=e.key.content;n=t.properties.some(e=>4===e.key.type&&e.key.content===i)}return n}function to(e,t){return`_${t}_${e.replace(/[^\w]/g,(t,n)=>"-"===t?"_":e.charCodeAt(n).toString())}`}function ta(e){return 14===e.type&&e.callee===ec?e.arguments[1].returns:e}let tl=/([\s\S]*?)\s+(?:in|of)\s+(\S[\s\S]*)/,tc={parseMode:"base",ns:0,delimiters:["{{","}}"],getNamespace:()=>0,isVoidTag:r,isPreTag:r,isCustomElement:r,onError:eF,onWarn:eB,comments:!1,prefixIdentifiers:!1},th=tc,td=null,tp="",tu=null,tf=null,tE="",t_=-1,tm=-1,tS=0,tg=!1,tT=null,tN=[],tI=new class{constructor(e,t){this.stack=e,this.cbs=t,this.state=1,this.buffer="",this.sectionStart=0,this.index=0,this.entityStart=0,this.baseState=1,this.inRCDATA=!1,this.inXML=!1,this.inVPre=!1,this.newlines=[],this.mode=0,this.delimiterOpen=ev,this.delimiterClose=ex,this.delimiterIndex=-1,this.currentSequence=void 0,this.sequenceIndex=0}get inSFCRoot(){return 2===this.mode&&0===this.stack.length}reset(){this.state=1,this.mode=0,this.buffer="",this.sectionStart=0,this.index=0,this.baseState=1,this.inRCDATA=!1,this.currentSequence=void 0,this.newlines.length=0,this.delimiterOpen=ev,this.delimiterClose=ex}getPos(e){let t=1,n=e+1;for(let i=this.newlines.length-1;i>=0;i--){let s=this.newlines[i];if(e>s){t=i+2,n=e-s;break}}return{column:n,line:t,offset:e}}peek(){return this.buffer.charCodeAt(this.index+1)}stateText(e){60===e?(this.index>this.sectionStart&&this.cbs.ontext(this.sectionStart,this.index),this.state=5,this.sectionStart=this.index):this.inVPre||e!==this.delimiterOpen[0]||(this.state=2,this.delimiterIndex=0,this.stateInterpolationOpen(e))}stateInterpolationOpen(e){if(e===this.delimiterOpen[this.delimiterIndex]){if(this.delimiterIndex===this.delimiterOpen.length-1){let e=this.index+1-this.delimiterOpen.length;e>this.sectionStart&&this.cbs.ontext(this.sectionStart,e),this.state=3,this.sectionStart=e}else this.delimiterIndex++}else this.inRCDATA?(this.state=32,this.stateInRCDATA(e)):(this.state=1,this.stateText(e))}stateInterpolation(e){e===this.delimiterClose[0]&&(this.state=4,this.delimiterIndex=0,this.stateInterpolationClose(e))}stateInterpolationClose(e){e===this.delimiterClose[this.delimiterIndex]?this.delimiterIndex===this.delimiterClose.length-1?(this.cbs.oninterpolation(this.sectionStart,this.index+1),this.inRCDATA?this.state=32:this.state=1,this.sectionStart=this.index+1):this.delimiterIndex++:(this.state=3,this.stateInterpolation(e))}stateSpecialStartSequence(e){let t=this.sequenceIndex===this.currentSequence.length;if(t?eD(e):(32|e)===this.currentSequence[this.sequenceIndex]){if(!t){this.sequenceIndex++;return}}else this.inRCDATA=!1;this.sequenceIndex=0,this.state=6,this.stateInTagName(e)}stateInRCDATA(e){if(this.sequenceIndex===this.currentSequence.length){if(62===e||eM(e)){let t=this.index-this.currentSequence.length;if(this.sectionStart<t){let e=this.index;this.index=t,this.cbs.ontext(this.sectionStart,t),this.index=e}this.sectionStart=t+2,this.stateInClosingTagName(e),this.inRCDATA=!1;return}this.sequenceIndex=0}(32|e)===this.currentSequence[this.sequenceIndex]?this.sequenceIndex+=1:0===this.sequenceIndex?this.currentSequence!==eV.TitleEnd&&(this.currentSequence!==eV.TextareaEnd||this.inSFCRoot)?this.fastForwardTo(60)&&(this.sequenceIndex=1):e===this.delimiterOpen[0]&&(this.state=2,this.delimiterIndex=0,this.stateInterpolationOpen(e)):this.sequenceIndex=Number(60===e)}stateCDATASequence(e){e===eV.Cdata[this.sequenceIndex]?++this.sequenceIndex===eV.Cdata.length&&(this.state=28,this.currentSequence=eV.CdataEnd,this.sequenceIndex=0,this.sectionStart=this.index+1):(this.sequenceIndex=0,this.state=23,this.stateInDeclaration(e))}fastForwardTo(e){for(;++this.index<this.buffer.length;){let t=this.buffer.charCodeAt(this.index);if(10===t&&this.newlines.push(this.index),t===e)return!0}return this.index=this.buffer.length-1,!1}stateInCommentLike(e){e===this.currentSequence[this.sequenceIndex]?++this.sequenceIndex===this.currentSequence.length&&(this.currentSequence===eV.CdataEnd?this.cbs.oncdata(this.sectionStart,this.index-2):this.cbs.oncomment(this.sectionStart,this.index-2),this.sequenceIndex=0,this.sectionStart=this.index+1,this.state=1):0===this.sequenceIndex?this.fastForwardTo(this.currentSequence[0])&&(this.sequenceIndex=1):e!==this.currentSequence[this.sequenceIndex-1]&&(this.sequenceIndex=0)}startSpecial(e,t){this.enterRCDATA(e,t),this.state=31}enterRCDATA(e,t){this.inRCDATA=!0,this.currentSequence=e,this.sequenceIndex=t}stateBeforeTagName(e){33===e?(this.state=22,this.sectionStart=this.index+1):63===e?(this.state=24,this.sectionStart=this.index+1):eL(e)?(this.sectionStart=this.index,0===this.mode?this.state=6:this.inSFCRoot?this.state=34:this.inXML?this.state=6:116===e?this.state=30:this.state=115===e?29:6):47===e?this.state=8:(this.state=1,this.stateText(e))}stateInTagName(e){eD(e)&&this.handleTagName(e)}stateInSFCRootTagName(e){if(eD(e)){let t=this.buffer.slice(this.sectionStart,this.index);"template"!==t&&this.enterRCDATA(eP("</"+t),0),this.handleTagName(e)}}handleTagName(e){this.cbs.onopentagname(this.sectionStart,this.index),this.sectionStart=-1,this.state=11,this.stateBeforeAttrName(e)}stateBeforeClosingTagName(e){eM(e)||(62===e?(this.state=1,this.sectionStart=this.index+1):(this.state=eL(e)?9:27,this.sectionStart=this.index))}stateInClosingTagName(e){(62===e||eM(e))&&(this.cbs.onclosetag(this.sectionStart,this.index),this.sectionStart=-1,this.state=10,this.stateAfterClosingTagName(e))}stateAfterClosingTagName(e){62===e&&(this.state=1,this.sectionStart=this.index+1)}stateBeforeAttrName(e){62===e?(this.cbs.onopentagend(this.index),this.inRCDATA?this.state=32:this.state=1,this.sectionStart=this.index+1):47===e?this.state=7:60===e&&47===this.peek()?(this.cbs.onopentagend(this.index),this.state=5,this.sectionStart=this.index):eM(e)||this.handleAttrStart(e)}handleAttrStart(e){118===e&&45===this.peek()?(this.state=13,this.sectionStart=this.index):46===e||58===e||64===e||35===e?(this.cbs.ondirname(this.index,this.index+1),this.state=14,this.sectionStart=this.index+1):(this.state=12,this.sectionStart=this.index)}stateInSelfClosingTag(e){62===e?(this.cbs.onselfclosingtag(this.index),this.state=1,this.sectionStart=this.index+1,this.inRCDATA=!1):eM(e)||(this.state=11,this.stateBeforeAttrName(e))}stateInAttrName(e){(61===e||eD(e))&&(this.cbs.onattribname(this.sectionStart,this.index),this.handleAttrNameEnd(e))}stateInDirName(e){61===e||eD(e)?(this.cbs.ondirname(this.sectionStart,this.index),this.handleAttrNameEnd(e)):58===e?(this.cbs.ondirname(this.sectionStart,this.index),this.state=14,this.sectionStart=this.index+1):46===e&&(this.cbs.ondirname(this.sectionStart,this.index),this.state=16,this.sectionStart=this.index+1)}stateInDirArg(e){61===e||eD(e)?(this.cbs.ondirarg(this.sectionStart,this.index),this.handleAttrNameEnd(e)):91===e?this.state=15:46===e&&(this.cbs.ondirarg(this.sectionStart,this.index),this.state=16,this.sectionStart=this.index+1)}stateInDynamicDirArg(e){93===e?this.state=14:(61===e||eD(e))&&(this.cbs.ondirarg(this.sectionStart,this.index+1),this.handleAttrNameEnd(e))}stateInDirModifier(e){61===e||eD(e)?(this.cbs.ondirmodifier(this.sectionStart,this.index),this.handleAttrNameEnd(e)):46===e&&(this.cbs.ondirmodifier(this.sectionStart,this.index),this.sectionStart=this.index+1)}handleAttrNameEnd(e){this.sectionStart=this.index,this.state=17,this.cbs.onattribnameend(this.index),this.stateAfterAttrName(e)}stateAfterAttrName(e){61===e?this.state=18:47===e||62===e?(this.cbs.onattribend(0,this.sectionStart),this.sectionStart=-1,this.state=11,this.stateBeforeAttrName(e)):eM(e)||(this.cbs.onattribend(0,this.sectionStart),this.handleAttrStart(e))}stateBeforeAttrValue(e){34===e?(this.state=19,this.sectionStart=this.index+1):39===e?(this.state=20,this.sectionStart=this.index+1):eM(e)||(this.sectionStart=this.index,this.state=21,this.stateInAttrValueNoQuotes(e))}handleInAttrValue(e,t){(e===t||this.fastForwardTo(t))&&(this.cbs.onattribdata(this.sectionStart,this.index),this.sectionStart=-1,this.cbs.onattribend(34===t?3:2,this.index+1),this.state=11)}stateInAttrValueDoubleQuotes(e){this.handleInAttrValue(e,34)}stateInAttrValueSingleQuotes(e){this.handleInAttrValue(e,39)}stateInAttrValueNoQuotes(e){eM(e)||62===e?(this.cbs.onattribdata(this.sectionStart,this.index),this.sectionStart=-1,this.cbs.onattribend(1,this.index),this.state=11,this.stateBeforeAttrName(e)):(39===e||60===e||61===e||96===e)&&this.cbs.onerr(18,this.index)}stateBeforeDeclaration(e){91===e?(this.state=26,this.sequenceIndex=0):this.state=45===e?25:23}stateInDeclaration(e){(62===e||this.fastForwardTo(62))&&(this.state=1,this.sectionStart=this.index+1)}stateInProcessingInstruction(e){(62===e||this.fastForwardTo(62))&&(this.cbs.onprocessinginstruction(this.sectionStart,this.index),this.state=1,this.sectionStart=this.index+1)}stateBeforeComment(e){45===e?(this.state=28,this.currentSequence=eV.CommentEnd,this.sequenceIndex=2,this.sectionStart=this.index+1):this.state=23}stateInSpecialComment(e){(62===e||this.fastForwardTo(62))&&(this.cbs.oncomment(this.sectionStart,this.index),this.state=1,this.sectionStart=this.index+1)}stateBeforeSpecialS(e){e===eV.ScriptEnd[3]?this.startSpecial(eV.ScriptEnd,4):e===eV.StyleEnd[3]?this.startSpecial(eV.StyleEnd,4):(this.state=6,this.stateInTagName(e))}stateBeforeSpecialT(e){e===eV.TitleEnd[3]?this.startSpecial(eV.TitleEnd,4):e===eV.TextareaEnd[3]?this.startSpecial(eV.TextareaEnd,4):(this.state=6,this.stateInTagName(e))}startEntity(){}stateInEntity(){}parse(e){for(this.buffer=e;this.index<this.buffer.length;){let e=this.buffer.charCodeAt(this.index);switch(10===e&&this.newlines.push(this.index),this.state){case 1:this.stateText(e);break;case 2:this.stateInterpolationOpen(e);break;case 3:this.stateInterpolation(e);break;case 4:this.stateInterpolationClose(e);break;case 31:this.stateSpecialStartSequence(e);break;case 32:this.stateInRCDATA(e);break;case 26:this.stateCDATASequence(e);break;case 19:this.stateInAttrValueDoubleQuotes(e);break;case 12:this.stateInAttrName(e);break;case 13:this.stateInDirName(e);break;case 14:this.stateInDirArg(e);break;case 15:this.stateInDynamicDirArg(e);break;case 16:this.stateInDirModifier(e);break;case 28:this.stateInCommentLike(e);break;case 27:this.stateInSpecialComment(e);break;case 11:this.stateBeforeAttrName(e);break;case 6:this.stateInTagName(e);break;case 34:this.stateInSFCRootTagName(e);break;case 9:this.stateInClosingTagName(e);break;case 5:this.stateBeforeTagName(e);break;case 17:this.stateAfterAttrName(e);break;case 20:this.stateInAttrValueSingleQuotes(e);break;case 18:this.stateBeforeAttrValue(e);break;case 8:this.stateBeforeClosingTagName(e);break;case 10:this.stateAfterClosingTagName(e);break;case 29:this.stateBeforeSpecialS(e);break;case 30:this.stateBeforeSpecialT(e);break;case 21:this.stateInAttrValueNoQuotes(e);break;case 7:this.stateInSelfClosingTag(e);break;case 23:this.stateInDeclaration(e);break;case 22:this.stateBeforeDeclaration(e);break;case 25:this.stateBeforeComment(e);break;case 24:this.stateInProcessingInstruction(e);break;case 33:this.stateInEntity()}this.index++}this.cleanup(),this.finish()}cleanup(){this.sectionStart!==this.index&&(1===this.state||32===this.state&&0===this.sequenceIndex?(this.cbs.ontext(this.sectionStart,this.index),this.sectionStart=this.index):(19===this.state||20===this.state||21===this.state)&&(this.cbs.onattribdata(this.sectionStart,this.index),this.sectionStart=this.index))}finish(){this.handleTrailingData(),this.cbs.onend()}handleTrailingData(){let e=this.buffer.length;this.sectionStart>=e||(28===this.state?this.currentSequence===eV.CdataEnd?this.cbs.oncdata(this.sectionStart,e):this.cbs.oncomment(this.sectionStart,e):6===this.state||11===this.state||18===this.state||17===this.state||12===this.state||13===this.state||14===this.state||15===this.state||16===this.state||20===this.state||19===this.state||21===this.state||9===this.state||this.cbs.ontext(this.sectionStart,e))}emitCodePoint(e,t){}}(tN,{onerr:tU,ontext(e,t){tb(tA(e,t),e,t)},ontextentity(e,t,n){tb(e,t,n)},oninterpolation(e,t){if(tg)return tb(tA(e,t),e,t);let n=e+tI.delimiterOpen.length,i=t-tI.delimiterClose.length;for(;eM(tp.charCodeAt(n));)n++;for(;eM(tp.charCodeAt(i-1));)i--;let s=tA(n,i);s.includes("&")&&(s=th.decodeEntities(s,!1)),tV({type:5,content:tw(s,!1,tk(n,i)),loc:tk(e,t)})},onopentagname(e,t){let n=tA(e,t);tu={type:1,tag:n,ns:th.getNamespace(n,tN[0],th.ns),tagType:0,props:[],children:[],loc:tk(e-1,t),codegenNode:void 0}},onopentagend(e){tC(e)},onclosetag(e,t){let n=tA(e,t);if(!th.isVoidTag(n)){let i=!1;for(let e=0;e<tN.length;e++)if(tN[e].tag.toLowerCase()===n.toLowerCase()){i=!0,e>0&&/* @__PURE__ *//*#__PURE__*/tN[0].loc.start.offset;for(let n=0;n<=e;n++)tR(tN.shift(),t,n<e);break}i||/* @__PURE__ *//*#__PURE__*/tv(e,60)}},onselfclosingtag(e){let t=tu.tag;tu.isSelfClosing=!0,tC(e),tN[0]&&tN[0].tag===t&&tR(tN.shift(),e)},onattribname(e,t){tf={type:6,name:tA(e,t),nameLoc:tk(e,t),value:void 0,loc:tk(e)}},ondirname(e,t){let n=tA(e,t),i="."===n||":"===n?"bind":"@"===n?"on":"#"===n?"slot":n.slice(2);if(tg||""===i)tf={type:6,name:n,nameLoc:tk(e,t),value:void 0,loc:tk(e)};else if(tf={type:7,name:i,rawName:n,exp:void 0,arg:void 0,modifiers:"."===n?["prop"]:[],loc:tk(e)},"pre"===i){tg=tI.inVPre=!0,tT=tu;let e=tu.props;for(let t=0;t<e.length;t++)7===e[t].type&&(e[t]=function(e){let t={type:6,name:e.rawName,nameLoc:tk(e.loc.start.offset,e.loc.start.offset+e.rawName.length),value:void 0,loc:e.loc};if(e.exp){let n=e.exp.loc;n.end.offset<e.loc.end.offset&&(n.start.offset--,n.start.column--,n.end.offset++,n.end.column++),t.value={type:2,content:e.exp.content,loc:n}}return t}(e[t]))}},ondirarg(e,t){if(e===t)return;let n=tA(e,t);if(tg)tf.name+=n,tX(tf.nameLoc,t);else{let i="["!==n[0];tf.arg=tw(i?n:n.slice(1,-1),i,tk(e,t),i?3:0)}},ondirmodifier(e,t){let n=tA(e,t);if(tg)tf.name+="."+n,tX(tf.nameLoc,t);else if("slot"===tf.name){let e=tf.arg;e&&(e.content+="."+n,tX(e.loc,t))}else tf.modifiers.push(n)},onattribdata(e,t){tE+=tA(e,t),t_<0&&(t_=e),tm=t},onattribentity(e,t,n){tE+=e,t_<0&&(t_=t),tm=n},onattribnameend(e){let t=tA(tf.loc.start.offset,e);7===tf.type&&(tf.rawName=t),tu.props.some(e=>(7===e.type?e.rawName:e.name)===t)},onattribend(e,t){if(tu&&tf){if(tX(tf.loc,t),0!==e){if(tE.includes("&")&&(tE=th.decodeEntities(tE,!0)),6===tf.type)"class"===tf.name&&(tE=tP(tE).trim()),tf.value={type:2,content:tE,loc:1===e?tk(t_,tm):tk(t_-1,tm+1)},tI.inSFCRoot&&"template"===tu.tag&&"lang"===tf.name&&tE&&"html"!==tE&&tI.enterRCDATA(eP("</template"),0);else{tf.exp=tw(tE,!1,tk(t_,tm),0,0),"for"===tf.name&&(tf.forParseResult=function(e){let t=e.loc,n=e.content,i=n.match(tl);if(!i)return;let[,s,r]=i,o=(e,n,i=!1)=>{let s=t.start.offset+n,r=s+e.length;return tw(e,!1,tk(s,r),0,i?1:0)},a={source:o(r.trim(),n.indexOf(r,s.length)),value:void 0,key:void 0,index:void 0,finalized:!1},l=s.trim().replace(tO,"").trim(),c=s.indexOf(l),h=l.match(ty);if(h){let e;l=l.replace(ty,"").trim();let t=h[1].trim();if(t&&(e=n.indexOf(t,c+l.length),a.key=o(t,e,!0)),h[2]){let i=h[2].trim();i&&(a.index=o(i,n.indexOf(i,a.key?e+t.length:c+l.length),!0))}}return l&&(a.value=o(l,c,!0)),a}(tf.exp));let e=-1;"bind"===tf.name&&(e=tf.modifiers.indexOf("sync"))>-1&&eU("COMPILER_V_BIND_SYNC",th,tf.loc,tf.rawName)&&(tf.name="model",tf.modifiers.splice(e,1))}}(7!==tf.type||"pre"!==tf.name)&&tu.props.push(tf)}tE="",t_=tm=-1},oncomment(e,t){th.comments&&tV({type:3,content:tA(e,t),loc:tk(e-4,t+3)})},onend(){let e=tp.length;for(let t=0;t<tN.length;t++)tR(tN[t],e-1),/* @__PURE__ *//*#__PURE__*/tN[t].loc.start.offset},oncdata(e,t){0!==tN[0].ns&&tb(tA(e,t),e,t)},onprocessinginstruction(e){(tN[0]?tN[0].ns:th.ns)===0&&tU(21,e-1)}}),ty=/,([^,\}\]]*)(?:,([^,\}\]]*))?$/,tO=/^\(|\)$/g;function tA(e,t){return tp.slice(e,t)}function tC(e){tI.inSFCRoot&&(tu.innerLoc=tk(e+1,e+1)),tV(tu);let{tag:t,ns:n}=tu;0===n&&th.isPreTag(t)&&tS++,th.isVoidTag(t)?tR(tu,e):(tN.unshift(tu),(1===n||2===n)&&(tI.inXML=!0)),tu=null}function tb(e,t,n){{let t=tN[0]&&tN[0].tag;"script"!==t&&"style"!==t&&e.includes("&")&&(e=th.decodeEntities(e,!1))}let i=tN[0]||td,s=i.children[i.children.length-1];s&&2===s.type?(s.content+=e,tX(s.loc,n)):i.children.push({type:2,content:e,loc:tk(t,n)})}function tR(e,t,n=!1){n?tX(e.loc,tv(t,60)):tX(e.loc,function(e,t){let n=e;for(;62!==tp.charCodeAt(n)&&n<tp.length-1;)n++;return n}(t,0)+1),tI.inSFCRoot&&(e.children.length?e.innerLoc.end=a({},e.children[e.children.length-1].loc.end):e.innerLoc.end=a({},e.innerLoc.start),e.innerLoc.source=tA(e.innerLoc.start.offset,e.innerLoc.end.offset));let{tag:i,ns:s}=e;!tg&&("slot"===i?e.tagType=2:tL(e)?e.tagType=3:function({tag:e,props:t}){var n;if(th.isCustomElement(e))return!1;if("component"===e||(n=e.charCodeAt(0))>64&&n<91||eW(e)||th.isBuiltInComponent&&th.isBuiltInComponent(e)||th.isNativeTag&&!th.isNativeTag(e))return!0;for(let e=0;e<t.length;e++){let n=t[e];if(6===n.type){if("is"===n.name&&n.value&&(n.value.content.startsWith("vue:")||eU("COMPILER_IS_ON_ELEMENT",th,n.loc)))return!0}else if("bind"===n.name&&e9(n.arg,"is")&&eU("COMPILER_IS_ON_ELEMENT",th,n.loc))return!0}return!1}(e)&&(e.tagType=1)),tI.inRCDATA||(e.children=tD(e.children,e.tag)),0===s&&th.isPreTag(i)&&tS--,tT===e&&(tg=tI.inVPre=!1,tT=null),tI.inXML&&(tN[0]?tN[0].ns:th.ns)===0&&(tI.inXML=!1);{let t=e.props;if(!tI.inSFCRoot&&ew("COMPILER_NATIVE_TEMPLATE",th)&&"template"===e.tag&&!tL(e)){let t=tN[0]||td,n=t.children.indexOf(e);t.children.splice(n,1,...e.children)}let n=t.find(e=>6===e.type&&"inline-template"===e.name);n&&eU("COMPILER_INLINE_TEMPLATE",th,n.loc)&&e.children.length&&(n.value={type:2,content:tA(e.children[0].loc.start.offset,e.children[e.children.length-1].loc.end.offset),loc:n.loc})}}function tv(e,t){let n=e;for(;tp.charCodeAt(n)!==t&&n>=0;)n--;return n}let tx=/* @__PURE__ */new Set(["if","else","else-if","for","slot"]);function tL({tag:e,props:t}){if("template"===e){for(let e=0;e<t.length;e++)if(7===t[e].type&&tx.has(t[e].name))return!0}return!1}let tM=/\r\n/g;function tD(e,t){let n="preserve"!==th.whitespace,i=!1;for(let t=0;t<e.length;t++){let s=e[t];if(2===s.type){if(tS)s.content=s.content.replace(tM,"\n");else if(function(e){for(let t=0;t<e.length;t++)if(!eM(e.charCodeAt(t)))return!1;return!0}(s.content)){let r=e[t-1]&&e[t-1].type,o=e[t+1]&&e[t+1].type;!r||!o||n&&(3===r&&(3===o||1===o)||1===r&&(3===o||1===o&&function(e){for(let t=0;t<e.length;t++){let n=e.charCodeAt(t);if(10===n||13===n)return!0}return!1}(s.content)))?(i=!0,e[t]=null):s.content=" "}else n&&(s.content=tP(s.content))}}if(tS&&t&&th.isPreTag(t)){let t=e[0];t&&2===t.type&&(t.content=t.content.replace(/^\r?\n/,""))}return i?e.filter(Boolean):e}function tP(e){let t="",n=!1;for(let i=0;i<e.length;i++)eM(e.charCodeAt(i))?n||(t+=" ",n=!0):(t+=e[i],n=!1);return t}function tV(e){(tN[0]||td).children.push(e)}function tk(e,t){return{start:tI.getPos(e),end:null==t?t:tI.getPos(t),source:null==t?t:tA(e,t)}}function tX(e,t){e.end=tI.getPos(t),e.source=tA(e.start.offset,t)}function tw(e,t=!1,n,i=0,s=0){return eg(e,t,n,i)}function /*#__PURE__*/tU(e,t,n){th.onError(/* @__PURE__ *//*#__PURE__*/e$(e,tk(t,t)))}function tF(e,t){if(tI.reset(),tu=null,tf=null,tE="",t_=-1,tm=-1,tN.length=0,tp=e,th=a({},tc),t){let e;for(e in t)null!=t[e]&&(th[e]=t[e])}tI.mode="html"===th.parseMode?1:"sfc"===th.parseMode?2:0,tI.inXML=1===th.ns||2===th.ns;let n=t&&t.delimiters;n&&(tI.delimiterOpen=eP(n[0]),tI.delimiterClose=eP(n[1]));let i=td=ef([],e);return tI.parse(tp),i.loc=tk(0,e.length),i.children=tD(i.children),td=null,i}function tB(e,t){let{children:n}=e;return 1===n.length&&1===t.type&&!tn(t)}function t$(e,t){let{constantCache:n}=t;switch(e.type){case 1:if(0!==e.tagType)return 0;let i=n.get(e);if(void 0!==i)return i;let s=e.codegenNode;if(13!==s.type||s.isBlock&&"svg"!==e.tag&&"foreignObject"!==e.tag&&"math"!==e.tag)return 0;if(void 0!==s.patchFlag)return n.set(e,0),0;{let i=3,r=tG(e,t);if(0===r)return n.set(e,0),0;r<i&&(i=r);for(let s=0;s<e.children.length;s++){let r=t$(e.children[s],t);if(0===r)return n.set(e,0),0;r<i&&(i=r)}if(i>1)for(let s=0;s<e.props.length;s++){let r=e.props[s];if(7===r.type&&"bind"===r.name&&r.exp){let s=t$(r.exp,t);if(0===s)return n.set(e,0),0;s<i&&(i=s)}}if(s.isBlock){for(let t=0;t<e.props.length;t++)if(7===e.props[t].type)return n.set(e,0),0;t.removeHelper(L),t.removeHelper(eb(t.inSSR,s.isComponent)),s.isBlock=!1,t.helper(eC(t.inSSR,s.isComponent))}return n.set(e,i),i}case 2:case 3:return 3;case 9:case 11:case 10:default:return 0;case 5:case 12:return t$(e.content,t);case 4:return e.constType;case 8:let r=3;for(let n=0;n<e.children.length;n++){let i=e.children[n];if(c(i)||h(i))continue;let s=t$(i,t);if(0===s)return 0;s<r&&(r=s)}return r;case 20:return 2}}let tH=/* @__PURE__ */new Set([K,Y,Q,z]);function tG(e,t){let n=3,i=tq(e);if(i&&15===i.type){let{properties:e}=i;for(let i=0;i<e.length;i++){let s;let{key:r,value:o}=e[i],a=t$(r,t);if(0===a)return a;if(a<n&&(n=a),0===(s=4===o.type?t$(o,t):14===o.type?function e(t,n){if(14===t.type&&!c(t.callee)&&tH.has(t.callee)){let i=t.arguments[0];if(4===i.type)return t$(i,n);if(14===i.type)return e(i,n)}return 0}(o,t):0))return s;s<n&&(n=s)}}return n}function tq(e){let t=e.codegenNode;if(13===t.type)return t.props}function tJ(e,{filename:t="",prefixIdentifiers:n=!1,hoistStatic:r=!1,hmr:o=!1,cacheHandlers:a=!1,nodeTransforms:l=[],directiveTransforms:h={},transformHoist:d=null,isBuiltInComponent:p=s,isCustomElement:u=s,expressionPlugins:f=[],scopeId:E=null,slotted:S=!0,ssr:g=!1,inSSR:T=!1,ssrCssVars:N="",bindingMetadata:I=i,inline:y=!1,isTS:O=!1,onError:A=eF,onWarn:C=eB,compatConfig:b}){let R=t.replace(/\?.*$/,"").match(/([^/\\]+)\.\w+$/),v={filename:t,selfName:R&&m(_(R[1])),prefixIdentifiers:n,hoistStatic:r,hmr:o,cacheHandlers:a,nodeTransforms:l,directiveTransforms:h,transformHoist:d,isBuiltInComponent:p,isCustomElement:u,expressionPlugins:f,scopeId:E,slotted:S,ssr:g,inSSR:T,ssrCssVars:N,bindingMetadata:I,inline:y,isTS:O,onError:A,onWarn:C,compatConfig:b,root:e,helpers:/* @__PURE__ */new Map,components:/* @__PURE__ */new Set,directives:/* @__PURE__ */new Set,hoists:[],imports:[],cached:[],constantCache:/* @__PURE__ */new WeakMap,temps:0,identifiers:/* @__PURE__ */Object.create(null),scopes:{vFor:0,vSlot:0,vPre:0,vOnce:0},parent:null,grandParent:null,currentNode:e,childIndex:0,inVOnce:!1,helper(e){let t=v.helpers.get(e)||0;return v.helpers.set(e,t+1),e},removeHelper(e){let t=v.helpers.get(e);if(t){let n=t-1;n?v.helpers.set(e,n):v.helpers.delete(e)}},helperString:e=>`_${ed[v.helper(e)]}`,replaceNode(e){v.parent.children[v.childIndex]=v.currentNode=e},removeNode(e){let t=v.parent.children,n=e?t.indexOf(e):v.currentNode?v.childIndex:-1;e&&e!==v.currentNode?v.childIndex>n&&(v.childIndex--,v.onNodeRemoved()):(v.currentNode=null,v.onNodeRemoved()),v.parent.children.splice(n,1)},onNodeRemoved:s,addIdentifiers(e){},removeIdentifiers(e){},hoist(e){c(e)&&(e=eg(e)),v.hoists.push(e);let t=eg(`_hoisted_${v.hoists.length}`,!1,e.loc,2);return t.hoisted=e,t},cache(e,t=!1){let n=eO(v.cached.length,e,t);return v.cached.push(n),n}};return v.filters=/* @__PURE__ */new Set,v}function tj(e,t){let n=tJ(e,t);tW(e,n),t.hoistStatic&&!function e(t,n,i,s=!1,r=!1){let{children:o}=t,a=[];for(let n=0;n<o.length;n++){let l=o[n];if(1===l.type&&0===l.tagType){let e=s?0:t$(l,i);if(e>0){if(e>=2){l.codegenNode.patchFlag=-1,a.push(l);continue}}else{let e=l.codegenNode;if(13===e.type){let t=e.patchFlag;if((void 0===t||512===t||1===t)&&tG(l,i)>=2){let t=tq(l);t&&(e.props=i.hoist(t))}e.dynamicProps&&(e.dynamicProps=i.hoist(e.dynamicProps))}}}else if(12===l.type&&(s?0:t$(l,i))>=2){a.push(l);continue}if(1===l.type){let n=1===l.tagType;n&&i.scopes.vSlot++,e(l,t,i,!1,r),n&&i.scopes.vSlot--}else if(11===l.type)e(l,t,i,1===l.children.length,!0);else if(9===l.type)for(let n=0;n<l.branches.length;n++)e(l.branches[n],t,i,1===l.branches[n].children.length,r)}let c=!1;if(a.length===o.length&&1===t.type){if(0===t.tagType&&t.codegenNode&&13===t.codegenNode.type&&l(t.codegenNode.children))t.codegenNode.children=h(e_(t.codegenNode.children)),c=!0;else if(1===t.tagType&&t.codegenNode&&13===t.codegenNode.type&&t.codegenNode.children&&!l(t.codegenNode.children)&&15===t.codegenNode.children.type){let e=d(t.codegenNode,"default");e&&(e.returns=h(e_(e.returns)),c=!0)}else if(3===t.tagType&&n&&1===n.type&&1===n.tagType&&n.codegenNode&&13===n.codegenNode.type&&n.codegenNode.children&&!l(n.codegenNode.children)&&15===n.codegenNode.children.type){let e=e6(t,"slot",!0),i=e&&e.arg&&d(n.codegenNode,e.arg);i&&(i.returns=h(e_(i.returns)),c=!0)}}if(!c)for(let e of a)e.codegenNode=i.cache(e.codegenNode);function h(e){let t=i.cache(e);return r&&i.hmr&&(t.needArraySpread=!0),t}function d(e,t){if(e.children&&!l(e.children)&&15===e.children.type){let n=e.children.properties.find(e=>e.key===t||e.key.content===t);return n&&n.value}}a.length&&i.transformHoist&&i.transformHoist(o,i,t)}(e,void 0,n,tB(e,e.children[0])),t.ssr||function(e,t){let{helper:n}=t,{children:i}=e;if(1===i.length){let n=i[0];if(tB(e,n)&&n.codegenNode){let i=n.codegenNode;13===i.type&&eR(i,t),e.codegenNode=i}else e.codegenNode=n}else i.length>1&&(e.codegenNode=eE(t,n(C),void 0,e.children,64,void 0,void 0,!0,void 0,!1))}(e,n),e.helpers=/* @__PURE__ */new Set([...n.helpers.keys()]),e.components=[...n.components],e.directives=[...n.directives],e.imports=n.imports,e.hoists=n.hoists,e.temps=n.temps,e.cached=n.cached,e.transformed=!0,e.filters=[...n.filters]}function tW(e,t){t.currentNode=e;let{nodeTransforms:n}=t,i=[];for(let s=0;s<n.length;s++){let r=n[s](e,t);if(r&&(l(r)?i.push(...r):i.push(r)),!t.currentNode)return;e=t.currentNode}switch(e.type){case 3:t.ssr||t.helper(k);break;case 5:t.ssr||t.helper(j);break;case 9:for(let n=0;n<e.branches.length;n++)tW(e.branches[n],t);break;case 10:case 11:case 1:case 0:!function(e,t){let n=0,i=()=>{n--};for(;n<e.children.length;n++){let s=e.children[n];c(s)||(t.grandParent=t.parent,t.parent=e,t.childIndex=n,t.onNodeRemoved=i,tW(s,t))}}(e,t)}t.currentNode=e;let s=i.length;for(;s--;)i[s]()}function tK(e,t){let n=c(e)?t=>t===e:t=>e.test(t);return(e,i)=>{if(1===e.type){let{props:s}=e;if(3===e.tagType&&s.some(te))return;let r=[];for(let o=0;o<s.length;o++){let a=s[o];if(7===a.type&&n(a.name)){s.splice(o,1),o--;let n=t(e,a,i);n&&r.push(n)}}return r}}}let tY="/*#__PURE__*/",tQ=e=>`${ed[e]}: _${ed[e]}`;function tz(e,t={}){let n=function(e,{mode:t="function",prefixIdentifiers:n="module"===t,sourceMap:i=!1,filename:s="template.vue.html",scopeId:r=null,optimizeImports:o=!1,runtimeGlobalName:a="Vue",runtimeModuleName:l="vue",ssrRuntimeModuleName:c="vue/server-renderer",ssr:h=!1,isTS:d=!1,inSSR:p=!1}){let u={mode:t,prefixIdentifiers:n,sourceMap:i,filename:s,scopeId:r,optimizeImports:o,runtimeGlobalName:a,runtimeModuleName:l,ssrRuntimeModuleName:c,ssr:h,isTS:d,inSSR:p,source:e.source,code:"",column:1,line:1,offset:0,indentLevel:0,pure:!1,map:void 0,helper:e=>`_${ed[e]}`,push(e,t=-2,n){u.code+=e},indent(){f(++u.indentLevel)},deindent(e=!1){e?--u.indentLevel:f(--u.indentLevel)},newline(){f(u.indentLevel)}};function f(e){u.push("\n"+"  ".repeat(e),0)}return u}(e,t);t.onContextCreated&&t.onContextCreated(n);let{mode:i,push:s,prefixIdentifiers:r,indent:o,deindent:a,newline:l,scopeId:c,ssr:h}=n,d=Array.from(e.helpers),p=d.length>0,u=!r&&"module"!==i;!function(e,t){let{ssr:n,prefixIdentifiers:i,push:s,newline:r,runtimeModuleName:o,runtimeGlobalName:a,ssrRuntimeModuleName:l}=t,c=Array.from(e.helpers);if(c.length>0&&(s(`const _Vue = ${a}
`,-1),e.hoists.length)){let e=[P,V,k,X,w].filter(e=>c.includes(e)).map(tQ).join(", ");s(`const { ${e} } = _Vue
`,-1)}(function(e,t){if(!e.length)return;t.pure=!0;let{push:n,newline:i}=t;i();for(let s=0;s<e.length;s++){let r=e[s];r&&(n(`const _hoisted_${s+1} = `),t2(r,t),i())}t.pure=!1})(e.hoists,t),r(),s("return ")}(e,n);let f=(h?["_ctx","_push","_parent","_attrs"]:["_ctx","_cache"]).join(", ");if(s(`function ${h?"ssrRender":"render"}(${f}) {`),o(),u&&(s("with (_ctx) {"),o(),p&&(s(`const { ${d.map(tQ).join(", ")} } = _Vue
`,-1),l())),e.components.length&&(tZ(e.components,"component",n),(e.directives.length||e.temps>0)&&l()),e.directives.length&&(tZ(e.directives,"directive",n),e.temps>0&&l()),e.filters&&e.filters.length&&(l(),tZ(e.filters,"filter",n),l()),e.temps>0){s("let ");for(let t=0;t<e.temps;t++)s(`${t>0?", ":""}_temp${t}`)}return(e.components.length||e.directives.length||e.temps)&&(s(`
`,0),l()),h||s("return "),e.codegenNode?t2(e.codegenNode,n):s("null"),u&&(a(),s("}")),a(),s("}"),{ast:e,code:n.code,preamble:"",map:n.map?n.map.toJSON():void 0}}function tZ(e,t,{helper:n,push:i,newline:s,isTS:r}){let o=n("filter"===t?$:"component"===t?U:B);for(let n=0;n<e.length;n++){let a=e[n],l=a.endsWith("__self");l&&(a=a.slice(0,-6)),i(`const ${to(a,t)} = ${o}(${JSON.stringify(a)}${l?", true":""})${r?"!":""}`),n<e.length-1&&s()}}function t1(e,t){let n=e.length>3;t.push("["),n&&t.indent(),t0(e,t,n),n&&t.deindent(),t.push("]")}function t0(e,t,n=!1,i=!0){let{push:s,newline:r}=t;for(let o=0;o<e.length;o++){let a=e[o];c(a)?s(a,-3):l(a)?t1(a,t):t2(a,t),o<e.length-1&&(n?(i&&s(","),r()):i&&s(", "))}}function t2(e,t){if(c(e)){t.push(e,-3);return}if(h(e)){t.push(t.helper(e));return}switch(e.type){case 1:case 9:case 11:case 12:t2(e.codegenNode,t);break;case 2:!function(e,t){t.push(JSON.stringify(e.content),-3,e)}(e,t);break;case 4:t3(e,t);break;case 5:!function(e,t){let{push:n,helper:i,pure:s}=t;s&&n(tY),n(`${i(j)}(`),t2(e.content,t),n(")")}(e,t);break;case 8:t4(e,t);break;case 3:!function(e,t){let{push:n,helper:i,pure:s}=t;s&&n(tY),n(`${i(k)}(${JSON.stringify(e.content)})`,-3,e)}(e,t);break;case 13:!function(e,t){let n;let{push:i,helper:s,pure:r}=t,{tag:o,props:a,children:l,patchFlag:c,dynamicProps:h,directives:d,isBlock:p,disableTracking:u,isComponent:f}=e;c&&(n=String(c)),d&&i(s(H)+"("),p&&i(`(${s(L)}(${u?"true":""}), `),r&&i(tY),i(s(p?eb(t.inSSR,f):eC(t.inSSR,f))+"(",-2,e),t0(function(e){let t=e.length;for(;t--&&null==e[t];);return e.slice(0,t+1).map(e=>e||"null")}([o,a,l,n,h]),t),i(")"),p&&i(")"),d&&(i(", "),t2(d,t),i(")"))}(e,t);break;case 14:!function(e,t){let{push:n,helper:i,pure:s}=t,r=c(e.callee)?e.callee:i(e.callee);s&&n(tY),n(r+"(",-2,e),t0(e.arguments,t),n(")")}(e,t);break;case 15:!function(e,t){let{push:n,indent:i,deindent:s,newline:r}=t,{properties:o}=e;if(!o.length){n("{}",-2,e);return}let a=o.length>1;n(a?"{":"{ "),a&&i();for(let e=0;e<o.length;e++){let{key:i,value:s}=o[e];!function(e,t){let{push:n}=t;8===e.type?(n("["),t4(e,t),n("]")):e.isStatic?n(eY(e.content)?e.content:JSON.stringify(e.content),-2,e):n(`[${e.content}]`,-3,e)}(i,t),n(": "),t2(s,t),e<o.length-1&&(n(","),r())}a&&s(),n(a?"}":" }")}(e,t);break;case 17:t1(e.elements,t);break;case 18:!function(e,t){let{push:n,indent:i,deindent:s}=t,{params:r,returns:o,body:a,newline:c,isSlot:h}=e;h&&n(`_${ed[eo]}(`),n("(",-2,e),l(r)?t0(r,t):r&&t2(r,t),n(") => "),(c||a)&&(n("{"),i()),o?(c&&n("return "),l(o)?t1(o,t):t2(o,t)):a&&t2(a,t),(c||a)&&(s(),n("}")),h&&(e.isNonScopedSlot&&n(", undefined, true"),n(")"))}(e,t);break;case 19:!function(e,t){let{test:n,consequent:i,alternate:s,newline:r}=e,{push:o,indent:a,deindent:l,newline:c}=t;if(4===n.type){let e=!eY(n.content);e&&o("("),t3(n,t),e&&o(")")}else o("("),t2(n,t),o(")");r&&a(),t.indentLevel++,r||o(" "),o("? "),t2(i,t),t.indentLevel--,r&&c(),r||o(" "),o(": ");let h=19===s.type;!h&&t.indentLevel++,t2(s,t),!h&&t.indentLevel--,r&&l(!0)}(e,t);break;case 20:!function(e,t){let{push:n,helper:i,indent:s,deindent:r,newline:o}=t,{needPauseTracking:a,needArraySpread:l}=e;l&&n("[...("),n(`_cache[${e.index}] || (`),a&&(s(),n(`${i(ei)}(-1),`),o(),n("(")),n(`_cache[${e.index}] = `),t2(e.value,t),a&&(n(`).cacheIndex = ${e.index},`),o(),n(`${i(ei)}(1),`),o(),n(`_cache[${e.index}]`),r()),n(")"),l&&n(")]")}(e,t);break;case 21:t0(e.body,t,!0,!1)}}function t3(e,t){let{content:n,isStatic:i}=e;t.push(i?JSON.stringify(n):n,-3,e)}function t4(e,t){for(let n=0;n<e.children.length;n++){let i=e.children[n];c(i)?t.push(i,-3):t2(i,t)}}function t6(e,t,n=!1,i=!1,s=Object.create(t.identifiers)){return e}let t5=tK(/^(if|else|else-if)$/,(e,t,n)=>t9(e,t,n,(e,t,i)=>{let s=n.parent.children,r=s.indexOf(e),o=0;for(;r-- >=0;){let e=s[r];e&&9===e.type&&(o+=e.branches.length)}return()=>{i?e.codegenNode=t8(t,o,n):function(e){for(;;)if(19===e.type){if(19!==e.alternate.type)return e;e=e.alternate}else 20===e.type&&(e=e.value)}(e.codegenNode).alternate=t8(t,o+e.branches.length-1,n)}}));function t9(e,t,n,i){if("else"!==t.name&&(!t.exp||!t.exp.content.trim())){let i=t.exp?t.exp.loc:e.loc;n.onError(/* @__PURE__ *//*#__PURE__*/e$(28,t.loc)),t.exp=eg("true",!1,i)}if("if"===t.name){let s=t7(e,t),r={type:9,loc:e.loc,branches:[s]};if(n.replaceNode(r),i)return i(r,s,!0)}else{let s=n.parent.children,r=s.indexOf(e);for(;r-- >=-1;){let o=s[r];if(o&&3===o.type||o&&2===o.type&&!o.content.trim().length){n.removeNode(o);continue}if(o&&9===o.type){"else-if"===t.name&&void 0===o.branches[o.branches.length-1].condition&&n.onError(/* @__PURE__ *//*#__PURE__*/e$(30,e.loc)),n.removeNode();let s=t7(e,t);o.branches.push(s);let r=i&&i(o,s,!1);tW(s,n),r&&r(),n.currentNode=null}else n.onError(/* @__PURE__ *//*#__PURE__*/e$(30,e.loc));break}}}function t7(e,t){let n=3===e.tagType;return{type:10,loc:e.loc,condition:"else"===t.name?void 0:t.exp,children:n&&!e6(e,"for")?e.children:[e],userKey:e5(e,"key"),isTemplateIf:n}}function t8(e,t,n){return e.condition?ey(e.condition,ne(e,t,n),eN(n.helper(k),['""',"true"])):ne(e,t,n)}function ne(e,t,n){let{helper:i}=n,s=eS("key",eg(`${t}`,!1,eu,2)),{children:r}=e,o=r[0];if(1!==r.length||1!==o.type){if(1!==r.length||11!==o.type)return eE(n,i(C),em([s]),r,64,void 0,void 0,!0,!1,!1,e.loc);{let e=o.codegenNode;return ts(e,s,n),e}}{let e=o.codegenNode,t=ta(e);return 13===t.type&&eR(t,n),ts(t,s,n),e}}let nt=(e,t,n)=>{let{modifiers:i,loc:s}=e,r=e.arg,{exp:o}=e;if(o&&4===o.type&&!o.content.trim()&&(o=void 0),!o){if(4!==r.type||!r.isStatic)return n.onError(e$(52,r.loc)),{props:[eS(r,eg("",!0,s))]};nn(e),o=e.exp}return 4!==r.type?(r.children.unshift("("),r.children.push(') || ""')):r.isStatic||(r.content=`${r.content} || ""`),i.includes("camel")&&(4===r.type?r.isStatic?r.content=_(r.content):r.content=`${n.helperString(ee)}(${r.content})`:(r.children.unshift(`${n.helperString(ee)}(`),r.children.push(")"))),!n.inSSR&&(i.includes("prop")&&ni(r,"."),i.includes("attr")&&ni(r,"^")),{props:[eS(r,o)]}},nn=(e,t)=>{let n=e.arg,i=_(n.content);e.exp=eg(i,!1,n.loc)},ni=(e,t)=>{4===e.type?e.isStatic?e.content=t+e.content:e.content=`\`${t}\${${e.content}}\``:(e.children.unshift(`'${t}' + (`),e.children.push(")"))},ns=tK("for",(e,t,n)=>{let{helper:i,removeHelper:s}=n;return nr(e,t,n,t=>{let r=eN(i(G),[t.source]),o=tt(e),a=e6(e,"memo"),l=e5(e,"key",!1,!0);l&&7===l.type&&!l.exp&&nn(l);let c=l&&(6===l.type?l.value?eg(l.value.content,!0):void 0:l.exp),h=l&&c?eS("key",c):null,d=4===t.source.type&&t.source.constType>0,p=d?64:l?128:256;return t.codegenNode=eE(n,i(C),void 0,r,p,void 0,void 0,!0,!d,!1,e.loc),()=>{let l;let{children:p}=t,u=1!==p.length||1!==p[0].type,f=tn(e)?e:o&&1===e.children.length&&tn(e.children[0])?e.children[0]:null;if(f?(l=f.codegenNode,o&&h&&ts(l,h,n)):u?l=eE(n,i(C),h?em([h]):void 0,e.children,64,void 0,void 0,!0,void 0,!1):(l=p[0].codegenNode,o&&h&&ts(l,h,n),!d!==l.isBlock&&(l.isBlock?(s(L),s(eb(n.inSSR,l.isComponent))):s(eC(n.inSSR,l.isComponent))),l.isBlock=!d,l.isBlock?(i(L),i(eb(n.inSSR,l.isComponent))):i(eC(n.inSSR,l.isComponent))),a){let e=eI(na(t.parseResult,[eg("_cached")]));e.body=eA([eT(["const _memo = (",a.exp,")"]),eT(["if (_cached",...c?[" && _cached.key === ",c]:[],` && ${n.helperString(eh)}(_cached, _memo)) return _cached`]),eT(["const _item = ",l]),eg("_item.memo = _memo"),eg("return _item")]),r.arguments.push(e,eg("_cache"),eg(String(n.cached.length))),n.cached.push(null)}else r.arguments.push(eI(na(t.parseResult),l,!0))}})});function nr(e,t,n,i){if(!t.exp){n.onError(/* @__PURE__ *//*#__PURE__*/e$(31,t.loc));return}let s=t.forParseResult;if(!s){n.onError(/* @__PURE__ *//*#__PURE__*/e$(32,t.loc));return}no(s);let{addIdentifiers:r,removeIdentifiers:o,scopes:a}=n,{source:l,value:c,key:h,index:d}=s,p={type:11,loc:t.loc,source:l,valueAlias:c,keyAlias:h,objectIndexAlias:d,parseResult:s,children:tt(e)?e.children:[e]};n.replaceNode(p),a.vFor++;let u=i&&i(p);return()=>{a.vFor--,u&&u()}}function no(e,t){e.finalized||(e.finalized=!0)}function na({value:e,key:t,index:n},i=[]){return function(e){let t=e.length;for(;t--&&!e[t];);return e.slice(0,t+1).map((e,t)=>e||eg("_".repeat(t+1),!1))}([e,t,n,...i])}let nl=eg("undefined",!1),nc=(e,t)=>{if(1===e.type&&(1===e.tagType||3===e.tagType)){let n=e6(e,"slot");if(n)return n.exp,t.scopes.vSlot++,()=>{t.scopes.vSlot--}}},nh=(e,t,n,i)=>eI(e,n,!1,!0,n.length?n[0].loc:i);function nd(e,t,n=nh){t.helper(eo);let{children:i,loc:s}=e,r=[],o=[],a=t.scopes.vSlot>0||t.scopes.vFor>0,l=e6(e,"slot",!0);if(l){let{arg:e,exp:t}=l;e&&!ej(e)&&(a=!0),r.push(eS(e||eg("default",!0),n(t,void 0,i,s)))}let c=!1,h=!1,d=[],p=/* @__PURE__ */new Set,u=0;for(let e=0;e<i.length;e++){let s,f,E,_;let m=i[e];if(!tt(m)||!(s=e6(m,"slot",!0))){3!==m.type&&d.push(m);continue}if(l){t.onError(/* @__PURE__ *//*#__PURE__*/e$(37,s.loc));break}c=!0;let{children:S,loc:g}=m,{arg:T=eg("default",!0),exp:N,loc:I}=s;ej(T)?f=T?T.content:"default":a=!0;let y=e6(m,"for"),O=n(N,y,S,g);if(E=e6(m,"if"))a=!0,o.push(ey(E.exp,np(T,O,u++),nl));else if(_=e6(m,/^else(-if)?$/,!0)){let n,s=e;for(;s--&&3===(n=i[s]).type;);if(n&&tt(n)&&e6(n,/^(else-)?if$/)){let e=o[o.length-1];for(;19===e.alternate.type;)e=e.alternate;e.alternate=_.exp?ey(_.exp,np(T,O,u++),nl):np(T,O,u++)}else t.onError(/* @__PURE__ *//*#__PURE__*/e$(30,_.loc))}else if(y){a=!0;let e=y.forParseResult;e?(no(e),o.push(eN(t.helper(G),[e.source,eI(na(e),np(T,O),!0)]))):t.onError(e$(32,y.loc))}else{if(f){if(p.has(f)){t.onError(e$(38,I));continue}p.add(f),"default"===f&&(h=!0)}r.push(eS(T,O))}}if(!l){let e=(e,i)=>{let r=n(e,void 0,i,s);return t.compatConfig&&(r.isNonScopedSlot=!0),eS("default",r)};c?d.length&&d.some(e=>(function e(t){return 2!==t.type&&12!==t.type||(2===t.type?!!t.content.trim():e(t.content))})(e))&&(h?t.onError(e$(39,d[0].loc)):r.push(e(void 0,d))):r.push(e(void 0,i))}let f=a?2:!function e(t){for(let n=0;n<t.length;n++){let i=t[n];switch(i.type){case 1:if(2===i.tagType||e(i.children))return!0;break;case 9:if(e(i.branches))return!0;break;case 10:case 11:if(e(i.children))return!0}}return!1}(e.children)?1:3,E=em(r.concat(eS("_",eg(f+"",!1))),s);return o.length&&(E=eN(t.helper(J),[E,e_(o)])),{slots:E,hasDynamicSlots:a}}function np(e,t,n){let i=[eS("name",e),eS("fn",t)];return null!=n&&i.push(eS("key",eg(String(n),!0))),em(i)}let nu=/* @__PURE__ */new WeakMap,nf=(e,t)=>function(){let n,i,s,r,o;if(!(1===(e=t.currentNode).type&&(0===e.tagType||1===e.tagType)))return;let{tag:a,props:l}=e,c=1===e.tagType,h=c?nE(e,t):`"${a}"`,p=d(h)&&h.callee===F,u=0,f=p||h===b||h===R||!c&&("svg"===a||"foreignObject"===a||"math"===a);if(l.length>0){let i=n_(e,t,void 0,c,p);n=i.props,u=i.patchFlag,r=i.dynamicPropNames;let s=i.directives;o=s&&s.length?e_(s.map(e=>nS(e,t))):void 0,i.shouldUseBlock&&(f=!0)}if(e.children.length>0){if(h===v&&(f=!0,u|=1024),c&&h!==b&&h!==v){let{slots:n,hasDynamicSlots:s}=nd(e,t);i=n,s&&(u|=1024)}else if(1===e.children.length&&h!==b){let n=e.children[0],s=n.type,r=5===s||8===s;r&&0===t$(n,t)&&(u|=1),i=r||2===s?n:e.children}else i=e.children}r&&r.length&&(s=function(e){let t="[";for(let n=0,i=e.length;n<i;n++)t+=JSON.stringify(e[n]),n<i-1&&(t+=", ");return t+"]"}(r)),e.codegenNode=eE(t,h,n,i,0===u?void 0:u,s,o,!!f,!1,c,e.loc)};function nE(e,t,n=!1){let{tag:i}=e,s=ng(i),r=e5(e,"is",!1,!0);if(r){if(s||ew("COMPILER_IS_ON_ELEMENT",t)){let e;if(6===r.type?e=r.value&&eg(r.value.content,!0):(e=r.exp)||(e=eg("is",!1,r.arg.loc)),e)return eN(t.helper(F),[e])}else 6===r.type&&r.value.content.startsWith("vue:")&&(i=r.value.content.slice(4))}let o=eW(i)||t.isBuiltInComponent(i);return o?(n||t.helper(o),o):(t.helper(U),t.components.add(i),to(i,"component"))}function n_(e,t,n=e.props,i,s,r=!1){let a;let{tag:l,loc:c,children:d}=e,f=[],E=[],_=[],m=d.length>0,S=!1,g=0,T=!1,N=!1,I=!1,y=!1,O=!1,A=!1,C=[],b=e=>{f.length&&(E.push(em(nm(f),c)),f=[]),e&&E.push(e)},R=()=>{t.scopes.vFor>0&&f.push(eS(eg("ref_for",!0),eg("true")))},v=({key:e,value:n})=>{if(ej(e)){let r=e.content,a=o(r);a&&(!i||s)&&"onclick"!==r.toLowerCase()&&"onUpdate:modelValue"!==r&&!p(r)&&(y=!0),a&&p(r)&&(A=!0),a&&14===n.type&&(n=n.arguments[0]),20===n.type||(4===n.type||8===n.type)&&t$(n,t)>0||("ref"===r?T=!0:"class"===r?N=!0:"style"===r?I=!0:"key"===r||C.includes(r)||C.push(r),i&&("class"===r||"style"===r)&&!C.includes(r)&&C.push(r))}else O=!0};for(let s=0;s<n.length;s++){let o=n[s];if(6===o.type){let{loc:e,name:n,nameLoc:i,value:s}=o;if("ref"===n&&(T=!0,R()),"is"===n&&(ng(l)||s&&s.content.startsWith("vue:")||ew("COMPILER_IS_ON_ELEMENT",t)))continue;f.push(eS(eg(n,!0,i),eg(s?s.content:"",!0,s?s.loc:e)))}else{let{name:n,arg:s,exp:a,loc:d,modifiers:p}=o,T="bind"===n,N="on"===n;if("slot"===n){i||t.onError(/* @__PURE__ *//*#__PURE__*/e$(40,d));continue}if("once"===n||"memo"===n||"is"===n||T&&e9(s,"is")&&(ng(l)||ew("COMPILER_IS_ON_ELEMENT",t))||N&&r)continue;if((T&&e9(s,"key")||N&&m&&e9(s,"vue:before-update"))&&(S=!0),T&&e9(s,"ref")&&R(),!s&&(T||N)){if(O=!0,a){if(T){if(R(),b(),ew("COMPILER_V_BIND_OBJECT_ORDER",t)){E.unshift(a);continue}E.push(a)}else b({type:14,loc:d,callee:t.helper(Z),arguments:i?[a]:[a,"true"]})}else t.onError(e$(T?34:35,d));continue}T&&p.includes("prop")&&(g|=32);let I=t.directiveTransforms[n];if(I){let{props:n,needRuntime:i}=I(o,e,t);r||n.forEach(v),N&&s&&!ej(s)?b(em(n,c)):f.push(...n),i&&(_.push(o),h(i)&&nu.set(o,i))}else!u(n)&&(_.push(o),m&&(S=!0))}}if(E.length?(b(),a=E.length>1?eN(t.helper(W),E,c):E[0]):f.length&&(a=em(nm(f),c)),O?g|=16:(N&&!i&&(g|=2),I&&!i&&(g|=4),C.length&&(g|=8),y&&(g|=32)),!S&&(0===g||32===g)&&(T||A||_.length>0)&&(g|=512),!t.inSSR&&a)switch(a.type){case 15:let x=-1,L=-1,M=!1;for(let e=0;e<a.properties.length;e++){let t=a.properties[e].key;ej(t)?"class"===t.content?x=e:"style"===t.content&&(L=e):t.isHandlerKey||(M=!0)}let D=a.properties[x],P=a.properties[L];M?a=eN(t.helper(Q),[a]):(D&&!ej(D.value)&&(D.value=eN(t.helper(K),[D.value])),P&&(I||4===P.value.type&&"["===P.value.content.trim()[0]||17===P.value.type)&&(P.value=eN(t.helper(Y),[P.value])));break;case 14:break;default:a=eN(t.helper(Q),[eN(t.helper(z),[a])])}return{props:a,directives:_,patchFlag:g,dynamicPropNames:C,shouldUseBlock:S}}function nm(e){let t=/* @__PURE__ */new Map,n=[];for(let i=0;i<e.length;i++){let s=e[i];if(8===s.key.type||!s.key.isStatic){n.push(s);continue}let r=s.key.content,a=t.get(r);a?("style"===r||"class"===r||o(r))&&(17===a.value.type?a.value.elements.push(s.value):a.value=e_([a.value,s.value],a.loc)):(t.set(r,s),n.push(s))}return n}function nS(e,t){let n=[],i=nu.get(e);i?n.push(t.helperString(i)):(t.helper(B),t.directives.add(e.name),n.push(to(e.name,"directive")));let{loc:s}=e;if(e.exp&&n.push(e.exp),e.arg&&(e.exp||n.push("void 0"),n.push(e.arg)),Object.keys(e.modifiers).length){e.arg||(e.exp||n.push("void 0"),n.push("void 0"));let t=eg("true",!1,s);n.push(em(e.modifiers.map(e=>eS(e,t)),s))}return e_(n,e.loc)}function ng(e){return"component"===e||"Component"===e}let nT=(e,t)=>{if(tn(e)){let{children:n,loc:i}=e,{slotName:s,slotProps:r}=nN(e,t),o=[t.prefixIdentifiers?"_ctx.$slots":"$slots",s,"{}","undefined","true"],a=2;r&&(o[2]=r,a=3),n.length&&(o[3]=eI([],n,!1,!1,i),a=4),t.scopeId&&!t.slotted&&(a=5),o.splice(a),e.codegenNode=eN(t.helper(q),o,i)}};function nN(e,t){let n,i='"default"',s=[];for(let t=0;t<e.props.length;t++){let n=e.props[t];if(6===n.type)n.value&&("name"===n.name?i=JSON.stringify(n.value.content):(n.name=_(n.name),s.push(n)));else if("bind"===n.name&&e9(n.arg,"name")){if(n.exp)i=n.exp;else if(n.arg&&4===n.arg.type){let e=_(n.arg.content);i=n.exp=eg(e,!1,n.arg.loc)}}else"bind"===n.name&&n.arg&&ej(n.arg)&&(n.arg.content=_(n.arg.content)),s.push(n)}if(s.length>0){let{props:i,directives:r}=n_(e,t,s,!1,!1);n=i,r.length&&t.onError(e$(36,r[0].loc))}return{slotName:i,slotProps:n}}let nI=(e,t,n,i)=>{let s;let{loc:r,modifiers:o,arg:a}=e;if(e.exp||o.length,4===a.type){if(a.isStatic){let e=a.content;e.startsWith("vue:")&&(e=`vnode-${e.slice(4)}`),s=eg(0!==t.tagType||e.startsWith("vnode")||!/[A-Z]/.test(e)?S(_(e)):`on:${e}`,!0,a.loc)}else s=eT([`${n.helperString(en)}(`,a,")"])}else(s=a).children.unshift(`${n.helperString(en)}(`),s.children.push(")");let l=e.exp;l&&!l.content.trim()&&(l=void 0);let c=n.cacheHandlers&&!l&&!n.inVOnce;if(l){let e=e0(l),t=!(e||e3(l)),n=l.content.includes(";");(t||c&&e)&&(l=eT([`${t?"$event":"(...args)"} => ${n?"{":"("}`,l,n?"}":")"]))}let h={props:[eS(s,l||eg("() => {}",!1,r))]};return i&&(h=i(h)),c&&(h.props[0].value=n.cache(h.props[0].value)),h.props.forEach(e=>e.key.isHandlerKey=!0),h},ny=(e,t)=>{if(0===e.type||1===e.type||11===e.type||10===e.type)return()=>{let n;let i=e.children,s=!1;for(let e=0;e<i.length;e++){let t=i[e];if(e8(t)){s=!0;for(let s=e+1;s<i.length;s++){let r=i[s];if(e8(r))n||(n=i[e]=eT([t],t.loc)),n.children.push(" + ",r),i.splice(s,1),s--;else{n=void 0;break}}}}if(s&&(1!==i.length||0!==e.type&&(1!==e.type||0!==e.tagType||e.props.find(e=>7===e.type&&!t.directiveTransforms[e.name])||"template"===e.tag)))for(let e=0;e<i.length;e++){let n=i[e];if(e8(n)||8===n.type){let s=[];(2!==n.type||" "!==n.content)&&s.push(n),t.ssr||0!==t$(n,t)||s.push("1"),i[e]={type:12,content:n,loc:n.loc,codegenNode:eN(t.helper(X),s)}}}}},nO=/* @__PURE__ */new WeakSet,nA=(e,t)=>{if(1===e.type&&e6(e,"once",!0)&&!nO.has(e)&&!t.inVOnce&&!t.inSSR)return nO.add(e),t.inVOnce=!0,t.helper(ei),()=>{t.inVOnce=!1;let e=t.currentNode;e.codegenNode&&(e.codegenNode=t.cache(e.codegenNode,!0))}},nC=(e,t,n)=>{let i;let{exp:s,arg:r}=e;if(!s)return n.onError(/* @__PURE__ *//*#__PURE__*/e$(41,e.loc)),nb();let o=s.loc.source,a=4===s.type?s.content:o,l=n.bindingMetadata[o];if("props"===l||"props-aliased"===l)return /* @__PURE__ */s.loc,nb();if(!a.trim()||!e0(s))return n.onError(/* @__PURE__ *//*#__PURE__*/e$(42,s.loc)),nb();let c=r||eg("modelValue",!0),h=r?ej(r)?`onUpdate:${_(r.content)}`:eT(['"onUpdate:" + ',r]):"onUpdate:modelValue",d=n.isTS?"($event: any)":"$event";i=eT([`${d} => ((`,s,") = $event)"]);let p=[eS(c,e.exp),eS(h,i)];if(e.modifiers.length&&1===t.tagType){let t=e.modifiers.map(e=>(eY(e)?e:JSON.stringify(e))+": true").join(", "),n=r?ej(r)?`${r.content}Modifiers`:eT([r,' + "Modifiers"']):"modelModifiers";p.push(eS(n,eg(`{ ${t} }`,!1,e.loc,2)))}return nb(p)};function nb(e=[]){return{props:e}}let nR=/[\w).+\-_$\]]/,nv=(e,t)=>{ew("COMPILER_FILTERS",t)&&(5===e.type?nx(e.content,t):1===e.type&&e.props.forEach(e=>{7===e.type&&"for"!==e.name&&e.exp&&nx(e.exp,t)}))};function nx(e,t){if(4===e.type)nL(e,t);else for(let n=0;n<e.children.length;n++){let i=e.children[n];"object"==typeof i&&(4===i.type?nL(i,t):8===i.type?nx(e,t):5===i.type&&nx(i.content,t))}}function nL(e,t){let n=e.content,i=!1,s=!1,r=!1,o=!1,a=0,l=0,c=0,h=0,d,p,u,f,E=[];for(u=0;u<n.length;u++)if(p=d,d=n.charCodeAt(u),i)39===d&&92!==p&&(i=!1);else if(s)34===d&&92!==p&&(s=!1);else if(r)96===d&&92!==p&&(r=!1);else if(o)47===d&&92!==p&&(o=!1);else if(124!==d||124===n.charCodeAt(u+1)||124===n.charCodeAt(u-1)||a||l||c){switch(d){case 34:s=!0;break;case 39:i=!0;break;case 96:r=!0;break;case 40:c++;break;case 41:c--;break;case 91:l++;break;case 93:l--;break;case 123:a++;break;case 125:a--}if(47===d){let e,t=u-1;for(;t>=0&&" "===(e=n.charAt(t));t--);e&&nR.test(e)||(o=!0)}}else void 0===f?(h=u+1,f=n.slice(0,u).trim()):_();function _(){E.push(n.slice(h,u).trim()),h=u+1}if(void 0===f?f=n.slice(0,u).trim():0!==h&&_(),E.length){for(u=0;u<E.length;u++)f=function(e,t,n){n.helper($);let i=t.indexOf("(");if(i<0)return n.filters.add(t),`${to(t,"filter")}(${e})`;{let s=t.slice(0,i),r=t.slice(i+1);return n.filters.add(s),`${to(s,"filter")}(${e}${")"!==r?","+r:r}`}}(f,E[u],t);e.content=f,e.ast=void 0}}let nM=/* @__PURE__ */new WeakSet,nD=(e,t)=>{if(1===e.type){let n=e6(e,"memo");if(!(!n||nM.has(e)))return nM.add(e),()=>{let i=e.codegenNode||t.currentNode.codegenNode;i&&13===i.type&&(1!==e.tagType&&eR(i,t),e.codegenNode=eN(t.helper(ec),[n.exp,eI(void 0,i),"_cache",String(t.cached.length)]),t.cached.push(null))}}};function nP(e){return[[nA,t5,nD,ns,nv,nT,nf,nc,ny],{on:nI,bind:nt,model:nC}]}function nV(e,t={}){let n=t.onError||eF,i="module"===t.mode;!0===t.prefixIdentifiers?n(/* @__PURE__ *//*#__PURE__*/e$(47)):i&&n(/* @__PURE__ *//*#__PURE__*/e$(48)),t.cacheHandlers&&n(/* @__PURE__ *//*#__PURE__*/e$(49)),t.scopeId&&!i&&n(/* @__PURE__ *//*#__PURE__*/e$(50));let s=a({},t,{prefixIdentifiers:!1}),r=c(e)?tF(e,s):e,[o,l]=nP();return tj(r,a({},s,{nodeTransforms:[...o,...t.nodeTransforms||[]],directiveTransforms:a({},l,t.directiveTransforms||{})})),tz(r,s)}let nk=()=>({props:[]}),nX=Symbol(""),nw=Symbol(""),nU=Symbol(""),nF=Symbol(""),nB=Symbol(""),n$=Symbol(""),nH=Symbol(""),nG=Symbol(""),nq=Symbol(""),nJ=Symbol("");ep({[nX]:"vModelRadio",[nw]:"vModelCheckbox",[nU]:"vModelText",[nF]:"vModelSelect",[nB]:"vModelDynamic",[n$]:"withModifiers",[nH]:"withKeys",[nG]:"vShow",[nq]:"Transition",[nJ]:"TransitionGroup"});let nj={parseMode:"html",isVoidTag:A,isNativeTag:e=>I(e)||y(e)||O(e),isPreTag:e=>"pre"===e,decodeEntities:function(e,n=!1){return(t||(t=document.createElement("div")),n)?(t.innerHTML=`<div foo="${e.replace(/"/g,"&quot;")}">`,t.children[0].getAttribute("foo")):(t.innerHTML=e,t.textContent)},isBuiltInComponent:e=>"Transition"===e||"transition"===e?nq:"TransitionGroup"===e||"transition-group"===e?nJ:void 0,getNamespace(e,t,n){let i=t?t.ns:n;if(t&&2===i){if("annotation-xml"===t.tag){if("svg"===e)return 1;t.props.some(e=>6===e.type&&"encoding"===e.name&&null!=e.value&&("text/html"===e.value.content||"application/xhtml+xml"===e.value.content))&&(i=0)}else/^m(?:[ions]|text)$/.test(t.tag)&&"mglyph"!==e&&"malignmark"!==e&&(i=0)}else t&&1===i&&("foreignObject"===t.tag||"desc"===t.tag||"title"===t.tag)&&(i=0);if(0===i){if("svg"===e)return 1;if("math"===e)return 2}return i}},nW=e=>{1===e.type&&e.props.forEach((t,n)=>{6===t.type&&"style"===t.name&&t.value&&(e.props[n]={type:7,name:"bind",arg:eg("style",!0,t.loc),exp:nK(t.value.content,t.loc),modifiers:[],loc:t.loc})})},nK=(e,t)=>eg(JSON.stringify(function(e){let t={};return e.replace(N,"").split(g).forEach(e=>{if(e){let n=e.split(T);n.length>1&&(t[n[0].trim()]=n[1].trim())}}),t}(e)),!1,t,3),nY=/* @__PURE__ */n("passive,once,capture"),nQ=/* @__PURE__ */n("stop,prevent,self,ctrl,shift,alt,meta,exact,middle"),nz=/* @__PURE__ */n("left,right"),nZ=/* @__PURE__ */n("onkeyup,onkeydown,onkeypress",!0),n1=(e,t,n,i)=>{let s=[],r=[],o=[];for(let i=0;i<t.length;i++){let a=t[i];"native"===a&&eU("COMPILER_V_ON_NATIVE",n)?o.push(a):nY(a)?o.push(a):nz(a)?ej(e)?nZ(e.content)?s.push(a):r.push(a):(s.push(a),r.push(a)):nQ(a)?r.push(a):s.push(a)}return{keyModifiers:s,nonKeyModifiers:r,eventOptionModifiers:o}},n0=(e,t)=>ej(e)&&"onclick"===e.content.toLowerCase()?eg(t,!0):4!==e.type?eT(["(",e,`) === "onClick" ? "${t}" : (`,e,")"]):e,n2=(e,t)=>{1===e.type&&0===e.tagType&&("script"===e.tag||"style"===e.tag)&&t.removeNode()},n3=[nW],n4={cloak:nk,html:(e,t,n)=>{let{exp:i,loc:s}=e;return i||n.onError(e$(53,s)),t.children.length&&(n.onError(e$(54,s)),t.children.length=0),{props:[eS(eg("innerHTML",!0,s),i||eg("",!0))]}},text:(e,t,n)=>{let{exp:i,loc:s}=e;return i||n.onError(e$(55,s)),t.children.length&&(n.onError(e$(56,s)),t.children.length=0),{props:[eS(eg("textContent",!0),i?t$(i,n)>0?i:eN(n.helperString(j),[i],s):eg("",!0))]}},model:(e,t,n)=>{let i=nC(e,t,n);if(!i.props.length||1===t.tagType)return i;e.arg&&n.onError(e$(58,e.arg.loc));let{tag:s}=t,r=n.isCustomElement(s);if("input"===s||"textarea"===s||"select"===s||r){let o=nU,a=!1;if("input"===s||r){let i=e5(t,"type");if(i){if(7===i.type)o=nB;else if(i.value)switch(i.value.content){case"radio":o=nX;break;case"checkbox":o=nw;break;case"file":a=!0,n.onError(e$(59,e.loc))}}else e7(t)&&(o=nB)}else"select"===s&&(o=nF);a||(i.needRuntime=n.helper(o))}else n.onError(e$(57,e.loc));return i.props=i.props.filter(e=>!(4===e.key.type&&"modelValue"===e.key.content)),i},on:(e,t,n)=>nI(e,t,n,t=>{let{modifiers:i}=e;if(!i.length)return t;let{key:s,value:r}=t.props[0],{keyModifiers:o,nonKeyModifiers:a,eventOptionModifiers:l}=n1(s,i,n,e.loc);if(a.includes("right")&&(s=n0(s,"onContextmenu")),a.includes("middle")&&(s=n0(s,"onMouseup")),a.length&&(r=eN(n.helper(n$),[r,JSON.stringify(a)])),o.length&&(!ej(s)||nZ(s.content))&&(r=eN(n.helper(nH),[r,JSON.stringify(o)])),l.length){let e=l.map(m).join("");s=ej(s)?eg(`${s.content}${e}`,!0):eT(["(",s,`) + "${e}"`])}return{props:[eS(s,r)]}}),show:(e,t,n)=>{let{exp:i,loc:s}=e;return!i&&n.onError(e$(61,s)),{props:[],needRuntime:n.helper(nG)}}};return e.BASE_TRANSITION=x,e.BindingTypes={DATA:"data",PROPS:"props",PROPS_ALIASED:"props-aliased",SETUP_LET:"setup-let",SETUP_CONST:"setup-const",SETUP_REACTIVE_CONST:"setup-reactive-const",SETUP_MAYBE_REF:"setup-maybe-ref",SETUP_REF:"setup-ref",OPTIONS:"options",LITERAL_CONST:"literal-const"},e.CAMELIZE=ee,e.CAPITALIZE=et,e.CREATE_BLOCK=M,e.CREATE_COMMENT=k,e.CREATE_ELEMENT_BLOCK=D,e.CREATE_ELEMENT_VNODE=V,e.CREATE_SLOTS=J,e.CREATE_STATIC=w,e.CREATE_TEXT=X,e.CREATE_VNODE=P,e.CompilerDeprecationTypes={COMPILER_IS_ON_ELEMENT:"COMPILER_IS_ON_ELEMENT",COMPILER_V_BIND_SYNC:"COMPILER_V_BIND_SYNC",COMPILER_V_BIND_OBJECT_ORDER:"COMPILER_V_BIND_OBJECT_ORDER",COMPILER_V_ON_NATIVE:"COMPILER_V_ON_NATIVE",COMPILER_V_IF_V_FOR_PRECEDENCE:"COMPILER_V_IF_V_FOR_PRECEDENCE",COMPILER_NATIVE_TEMPLATE:"COMPILER_NATIVE_TEMPLATE",COMPILER_INLINE_TEMPLATE:"COMPILER_INLINE_TEMPLATE",COMPILER_FILTERS:"COMPILER_FILTERS"},e.ConstantTypes={NOT_CONSTANT:0,0:"NOT_CONSTANT",CAN_SKIP_PATCH:1,1:"CAN_SKIP_PATCH",CAN_CACHE:2,2:"CAN_CACHE",CAN_STRINGIFY:3,3:"CAN_STRINGIFY"},e.DOMDirectiveTransforms=n4,e.DOMErrorCodes={X_V_HTML_NO_EXPRESSION:53,53:"X_V_HTML_NO_EXPRESSION",X_V_HTML_WITH_CHILDREN:54,54:"X_V_HTML_WITH_CHILDREN",X_V_TEXT_NO_EXPRESSION:55,55:"X_V_TEXT_NO_EXPRESSION",X_V_TEXT_WITH_CHILDREN:56,56:"X_V_TEXT_WITH_CHILDREN",X_V_MODEL_ON_INVALID_ELEMENT:57,57:"X_V_MODEL_ON_INVALID_ELEMENT",X_V_MODEL_ARG_ON_ELEMENT:58,58:"X_V_MODEL_ARG_ON_ELEMENT",X_V_MODEL_ON_FILE_INPUT_ELEMENT:59,59:"X_V_MODEL_ON_FILE_INPUT_ELEMENT",X_V_MODEL_UNNECESSARY_VALUE:60,60:"X_V_MODEL_UNNECESSARY_VALUE",X_V_SHOW_NO_EXPRESSION:61,61:"X_V_SHOW_NO_EXPRESSION",X_TRANSITION_INVALID_CHILDREN:62,62:"X_TRANSITION_INVALID_CHILDREN",X_IGNORED_SIDE_EFFECT_TAG:63,63:"X_IGNORED_SIDE_EFFECT_TAG",__EXTEND_POINT__:64,64:"__EXTEND_POINT__"},e.DOMErrorMessages={53:"v-html is missing expression.",54:"v-html will override element children.",55:"v-text is missing expression.",56:"v-text will override element children.",57:"v-model can only be used on <input>, <textarea> and <select> elements.",58:"v-model argument is not supported on plain elements.",59:"v-model cannot be used on file inputs since they are read-only. Use a v-on:change listener instead.",60:"Unnecessary value binding used alongside v-model. It will interfere with v-model's behavior.",61:"v-show is missing expression.",62:"<Transition> expects exactly one child element or component.",63:"Tags with side effect (<script> and <style>) are ignored in client component templates."},e.DOMNodeTransforms=n3,e.ElementTypes={ELEMENT:0,0:"ELEMENT",COMPONENT:1,1:"COMPONENT",SLOT:2,2:"SLOT",TEMPLATE:3,3:"TEMPLATE"},e.ErrorCodes={ABRUPT_CLOSING_OF_EMPTY_COMMENT:0,0:"ABRUPT_CLOSING_OF_EMPTY_COMMENT",CDATA_IN_HTML_CONTENT:1,1:"CDATA_IN_HTML_CONTENT",DUPLICATE_ATTRIBUTE:2,2:"DUPLICATE_ATTRIBUTE",END_TAG_WITH_ATTRIBUTES:3,3:"END_TAG_WITH_ATTRIBUTES",END_TAG_WITH_TRAILING_SOLIDUS:4,4:"END_TAG_WITH_TRAILING_SOLIDUS",EOF_BEFORE_TAG_NAME:5,5:"EOF_BEFORE_TAG_NAME",EOF_IN_CDATA:6,6:"EOF_IN_CDATA",EOF_IN_COMMENT:7,7:"EOF_IN_COMMENT",EOF_IN_SCRIPT_HTML_COMMENT_LIKE_TEXT:8,8:"EOF_IN_SCRIPT_HTML_COMMENT_LIKE_TEXT",EOF_IN_TAG:9,9:"EOF_IN_TAG",INCORRECTLY_CLOSED_COMMENT:10,10:"INCORRECTLY_CLOSED_COMMENT",INCORRECTLY_OPENED_COMMENT:11,11:"INCORRECTLY_OPENED_COMMENT",INVALID_FIRST_CHARACTER_OF_TAG_NAME:12,12:"INVALID_FIRST_CHARACTER_OF_TAG_NAME",MISSING_ATTRIBUTE_VALUE:13,13:"MISSING_ATTRIBUTE_VALUE",MISSING_END_TAG_NAME:14,14:"MISSING_END_TAG_NAME",MISSING_WHITESPACE_BETWEEN_ATTRIBUTES:15,15:"MISSING_WHITESPACE_BETWEEN_ATTRIBUTES",NESTED_COMMENT:16,16:"NESTED_COMMENT",UNEXPECTED_CHARACTER_IN_ATTRIBUTE_NAME:17,17:"UNEXPECTED_CHARACTER_IN_ATTRIBUTE_NAME",UNEXPECTED_CHARACTER_IN_UNQUOTED_ATTRIBUTE_VALUE:18,18:"UNEXPECTED_CHARACTER_IN_UNQUOTED_ATTRIBUTE_VALUE",UNEXPECTED_EQUALS_SIGN_BEFORE_ATTRIBUTE_NAME:19,19:"UNEXPECTED_EQUALS_SIGN_BEFORE_ATTRIBUTE_NAME",UNEXPECTED_NULL_CHARACTER:20,20:"UNEXPECTED_NULL_CHARACTER",UNEXPECTED_QUESTION_MARK_INSTEAD_OF_TAG_NAME:21,21:"UNEXPECTED_QUESTION_MARK_INSTEAD_OF_TAG_NAME",UNEXPECTED_SOLIDUS_IN_TAG:22,22:"UNEXPECTED_SOLIDUS_IN_TAG",X_INVALID_END_TAG:23,23:"X_INVALID_END_TAG",X_MISSING_END_TAG:24,24:"X_MISSING_END_TAG",X_MISSING_INTERPOLATION_END:25,25:"X_MISSING_INTERPOLATION_END",X_MISSING_DIRECTIVE_NAME:26,26:"X_MISSING_DIRECTIVE_NAME",X_MISSING_DYNAMIC_DIRECTIVE_ARGUMENT_END:27,27:"X_MISSING_DYNAMIC_DIRECTIVE_ARGUMENT_END",X_V_IF_NO_EXPRESSION:28,28:"X_V_IF_NO_EXPRESSION",X_V_IF_SAME_KEY:29,29:"X_V_IF_SAME_KEY",X_V_ELSE_NO_ADJACENT_IF:30,30:"X_V_ELSE_NO_ADJACENT_IF",X_V_FOR_NO_EXPRESSION:31,31:"X_V_FOR_NO_EXPRESSION",X_V_FOR_MALFORMED_EXPRESSION:32,32:"X_V_FOR_MALFORMED_EXPRESSION",X_V_FOR_TEMPLATE_KEY_PLACEMENT:33,33:"X_V_FOR_TEMPLATE_KEY_PLACEMENT",X_V_BIND_NO_EXPRESSION:34,34:"X_V_BIND_NO_EXPRESSION",X_V_ON_NO_EXPRESSION:35,35:"X_V_ON_NO_EXPRESSION",X_V_SLOT_UNEXPECTED_DIRECTIVE_ON_SLOT_OUTLET:36,36:"X_V_SLOT_UNEXPECTED_DIRECTIVE_ON_SLOT_OUTLET",X_V_SLOT_MIXED_SLOT_USAGE:37,37:"X_V_SLOT_MIXED_SLOT_USAGE",X_V_SLOT_DUPLICATE_SLOT_NAMES:38,38:"X_V_SLOT_DUPLICATE_SLOT_NAMES",X_V_SLOT_EXTRANEOUS_DEFAULT_SLOT_CHILDREN:39,39:"X_V_SLOT_EXTRANEOUS_DEFAULT_SLOT_CHILDREN",X_V_SLOT_MISPLACED:40,40:"X_V_SLOT_MISPLACED",X_V_MODEL_NO_EXPRESSION:41,41:"X_V_MODEL_NO_EXPRESSION",X_V_MODEL_MALFORMED_EXPRESSION:42,42:"X_V_MODEL_MALFORMED_EXPRESSION",X_V_MODEL_ON_SCOPE_VARIABLE:43,43:"X_V_MODEL_ON_SCOPE_VARIABLE",X_V_MODEL_ON_PROPS:44,44:"X_V_MODEL_ON_PROPS",X_INVALID_EXPRESSION:45,45:"X_INVALID_EXPRESSION",X_KEEP_ALIVE_INVALID_CHILDREN:46,46:"X_KEEP_ALIVE_INVALID_CHILDREN",X_PREFIX_ID_NOT_SUPPORTED:47,47:"X_PREFIX_ID_NOT_SUPPORTED",X_MODULE_MODE_NOT_SUPPORTED:48,48:"X_MODULE_MODE_NOT_SUPPORTED",X_CACHE_HANDLER_NOT_SUPPORTED:49,49:"X_CACHE_HANDLER_NOT_SUPPORTED",X_SCOPE_ID_NOT_SUPPORTED:50,50:"X_SCOPE_ID_NOT_SUPPORTED",X_VNODE_HOOKS:51,51:"X_VNODE_HOOKS",X_V_BIND_INVALID_SAME_NAME_ARGUMENT:52,52:"X_V_BIND_INVALID_SAME_NAME_ARGUMENT",__EXTEND_POINT__:53,53:"__EXTEND_POINT__"},e.FRAGMENT=C,e.GUARD_REACTIVE_PROPS=z,e.IS_MEMO_SAME=eh,e.IS_REF=el,e.KEEP_ALIVE=v,e.MERGE_PROPS=W,e.NORMALIZE_CLASS=K,e.NORMALIZE_PROPS=Q,e.NORMALIZE_STYLE=Y,e.Namespaces={HTML:0,0:"HTML",SVG:1,1:"SVG",MATH_ML:2,2:"MATH_ML"},e.NodeTypes={ROOT:0,0:"ROOT",ELEMENT:1,1:"ELEMENT",TEXT:2,2:"TEXT",COMMENT:3,3:"COMMENT",SIMPLE_EXPRESSION:4,4:"SIMPLE_EXPRESSION",INTERPOLATION:5,5:"INTERPOLATION",ATTRIBUTE:6,6:"ATTRIBUTE",DIRECTIVE:7,7:"DIRECTIVE",COMPOUND_EXPRESSION:8,8:"COMPOUND_EXPRESSION",IF:9,9:"IF",IF_BRANCH:10,10:"IF_BRANCH",FOR:11,11:"FOR",TEXT_CALL:12,12:"TEXT_CALL",VNODE_CALL:13,13:"VNODE_CALL",JS_CALL_EXPRESSION:14,14:"JS_CALL_EXPRESSION",JS_OBJECT_EXPRESSION:15,15:"JS_OBJECT_EXPRESSION",JS_PROPERTY:16,16:"JS_PROPERTY",JS_ARRAY_EXPRESSION:17,17:"JS_ARRAY_EXPRESSION",JS_FUNCTION_EXPRESSION:18,18:"JS_FUNCTION_EXPRESSION",JS_CONDITIONAL_EXPRESSION:19,19:"JS_CONDITIONAL_EXPRESSION",JS_CACHE_EXPRESSION:20,20:"JS_CACHE_EXPRESSION",JS_BLOCK_STATEMENT:21,21:"JS_BLOCK_STATEMENT",JS_TEMPLATE_LITERAL:22,22:"JS_TEMPLATE_LITERAL",JS_IF_STATEMENT:23,23:"JS_IF_STATEMENT",JS_ASSIGNMENT_EXPRESSION:24,24:"JS_ASSIGNMENT_EXPRESSION",JS_SEQUENCE_EXPRESSION:25,25:"JS_SEQUENCE_EXPRESSION",JS_RETURN_STATEMENT:26,26:"JS_RETURN_STATEMENT"},e.OPEN_BLOCK=L,e.POP_SCOPE_ID=er,e.PUSH_SCOPE_ID=es,e.RENDER_LIST=G,e.RENDER_SLOT=q,e.RESOLVE_COMPONENT=U,e.RESOLVE_DIRECTIVE=B,e.RESOLVE_DYNAMIC_COMPONENT=F,e.RESOLVE_FILTER=$,e.SET_BLOCK_TRACKING=ei,e.SUSPENSE=R,e.TELEPORT=b,e.TO_DISPLAY_STRING=j,e.TO_HANDLERS=Z,e.TO_HANDLER_KEY=en,e.TRANSITION=nq,e.TRANSITION_GROUP=nJ,e.TS_NODE_TYPES=eJ,e.UNREF=ea,e.V_MODEL_CHECKBOX=nw,e.V_MODEL_DYNAMIC=nB,e.V_MODEL_RADIO=nX,e.V_MODEL_SELECT=nF,e.V_MODEL_TEXT=nU,e.V_ON_WITH_KEYS=nH,e.V_ON_WITH_MODIFIERS=n$,e.V_SHOW=nG,e.WITH_CTX=eo,e.WITH_DIRECTIVES=H,e.WITH_MEMO=ec,e.advancePositionWithClone=function(e,t,n=t.length){return e4({offset:e.offset,line:e.line,column:e.column},t,n)},e.advancePositionWithMutation=e4,e.assert=function(e,t){if(!e)throw Error(t||"unexpected compiler condition")},e.baseCompile=nV,e.baseParse=tF,e.buildDirectiveArgs=nS,e.buildProps=n_,e.buildSlots=nd,e.checkCompatEnabled=eU,e.compile=function(e,t={}){return nV(e,a({},nj,t,{nodeTransforms:[n2,...n3,...t.nodeTransforms||[]],directiveTransforms:a({},n4,t.directiveTransforms||{}),transformHoist:null}))},e.convertToBlock=eR,e.createArrayExpression=e_,e.createAssignmentExpression=function(e,t){return{type:24,left:e,right:t,loc:eu}},e.createBlockStatement=eA,e.createCacheExpression=eO,e.createCallExpression=eN,e.createCompilerError=e$,e.createCompoundExpression=eT,e.createConditionalExpression=ey,e.createDOMCompilerError=function(e,t){return e$(e,t)},e.createForLoopParams=na,e.createFunctionExpression=eI,e.createIfStatement=function(e,t,n){return{type:23,test:e,consequent:t,alternate:n,loc:eu}},e.createInterpolation=function(e,t){return{type:5,loc:t,content:c(e)?eg(e,!1,t):e}},e.createObjectExpression=em,e.createObjectProperty=eS,e.createReturnStatement=function(e){return{type:26,returns:e,loc:eu}},e.createRoot=ef,e.createSequenceExpression=function(e){return{type:25,expressions:e,loc:eu}},e.createSimpleExpression=eg,e.createStructuralDirectiveTransform=tK,e.createTemplateLiteral=function(e){return{type:22,elements:e,loc:eu}},e.createTransformContext=tJ,e.createVNodeCall=eE,e.errorMessages=eH,e.extractIdentifiers=eG,e.findDir=e6,e.findProp=e5,e.forAliasRE=tl,e.generate=tz,e.generateCodeFrame=function(e,t=0,n=e.length){if((t=Math.max(0,Math.min(t,e.length)))>(n=Math.max(0,Math.min(n,e.length))))return"";let i=e.split(/(\r?\n)/),s=i.filter((e,t)=>t%2==1);i=i.filter((e,t)=>t%2==0);let r=0,o=[];for(let e=0;e<i.length;e++)if((r+=i[e].length+(s[e]&&s[e].length||0))>=t){for(let a=e-2;a<=e+2||n>r;a++){if(a<0||a>=i.length)continue;let l=a+1;o.push(`${l}${" ".repeat(Math.max(3-String(l).length,0))}|  ${i[a]}`);let c=i[a].length,h=s[a]&&s[a].length||0;if(a===e){let e=t-(r-(c+h)),i=Math.max(1,n>r?c-e:n-t);o.push("   |  "+" ".repeat(e)+"^".repeat(i))}else if(a>e){if(n>r){let e=Math.max(Math.min(n-r,c),1);o.push("   |  "+"^".repeat(e))}r+=c+h}}break}return o.join("\n")},e.getBaseTransformPreset=nP,e.getConstantType=t$,e.getMemoedVNodeCall=ta,e.getVNodeBlockHelper=eb,e.getVNodeHelper=eC,e.hasDynamicKeyVBind=e7,e.hasScopeRef=function e(t,n){if(!t||0===Object.keys(n).length)return!1;switch(t.type){case 1:for(let i=0;i<t.props.length;i++){let s=t.props[i];if(7===s.type&&(e(s.arg,n)||e(s.exp,n)))return!0}return t.children.some(t=>e(t,n));case 11:if(e(t.source,n))return!0;return t.children.some(t=>e(t,n));case 9:return t.branches.some(t=>e(t,n));case 10:if(e(t.condition,n))return!0;return t.children.some(t=>e(t,n));case 4:return!t.isStatic&&eY(t.content)&&!!n[t.content];case 8:return t.children.some(t=>d(t)&&e(t,n));case 5:case 12:return e(t.content,n);default:return!1}},e.helperNameMap=ed,e.injectProp=ts,e.isCoreComponent=eW,e.isFnExpression=e3,e.isFnExpressionBrowser=e3,e.isFnExpressionNode=s,e.isFunctionType=e=>/Function(?:Expression|Declaration)$|Method$/.test(e.type),e.isInDestructureAssignment=function(e,t){if(e&&("ObjectProperty"===e.type||"ArrayPattern"===e.type)){let e=t.length;for(;e--;){let n=t[e];if("AssignmentExpression"===n.type)return!0;if("ObjectProperty"!==n.type&&!n.type.endsWith("Pattern"))break}}return!1},e.isInNewExpression=function(e){let t=e.length;for(;t--;){let n=e[t];if("NewExpression"===n.type)return!0;if("MemberExpression"!==n.type)break}return!1},e.isMemberExpression=e0,e.isMemberExpressionBrowser=e0,e.isMemberExpressionNode=s,e.isReferencedIdentifier=function(e,t,n){return!1},e.isSimpleIdentifier=eY,e.isSlotOutlet=tn,e.isStaticArgOf=e9,e.isStaticExp=ej,e.isStaticProperty=eq,e.isStaticPropertyKey=(e,t)=>eq(t)&&t.key===e,e.isTemplateNode=tt,e.isText=e8,e.isVSlot=te,e.locStub=eu,e.noopDirectiveTransform=nk,e.parse=function(e,t={}){return tF(e,a({},nj,t))},e.parserOptions=nj,e.processExpression=t6,e.processFor=nr,e.processIf=t9,e.processSlotOutlet=nN,e.registerRuntimeHelpers=ep,e.resolveComponentType=nE,e.stringifyExpression=function e(t){return c(t)?t:4===t.type?t.content:t.children.map(e).join("")},e.toValidAssetId=to,e.trackSlotScopes=nc,e.trackVForSlotScopes=(e,t)=>{let n;if(tt(e)&&e.props.some(te)&&(n=e6(e,"for"))){let e=n.forParseResult;if(e){no(e);let{value:n,key:i,index:s}=e,{addIdentifiers:r,removeIdentifiers:o}=t;return n&&r(n),i&&r(i),s&&r(s),()=>{n&&o(n),i&&o(i),s&&o(s)}}}},e.transform=tj,e.transformBind=nt,e.transformElement=nf,e.transformExpression=(e,t)=>{if(5===e.type)e.content=t6(e.content,t);else if(1===e.type)for(let n=0;n<e.props.length;n++){let i=e.props[n];if(7===i.type&&"for"!==i.name){let e=i.exp,n=i.arg;e&&4===e.type&&!("on"===i.name&&n)&&(i.exp=t6(e,t,"slot"===i.name)),n&&4===n.type&&!n.isStatic&&(i.arg=t6(n,t))}}},e.transformModel=nC,e.transformOn=nI,e.transformStyle=nW,e.traverseNode=tW,e.unwrapTSNode=function e(t){return eJ.includes(t.type)?e(t.expression):t},e.walkBlockDeclarations=function(e,t){for(let n of e.body)if("VariableDeclaration"===n.type){if(n.declare)continue;for(let e of n.declarations)for(let n of eG(e.id))t(n)}else if("FunctionDeclaration"===n.type||"ClassDeclaration"===n.type){if(n.declare||!n.id)continue;t(n.id)}else("ForOfStatement"===n.type||"ForInStatement"===n.type||"ForStatement"===n.type)&&function(e,t,n){let i="ForStatement"===e.type?e.init:e.left;if(i&&"VariableDeclaration"===i.type&&("var"===i.kind?t:!t))for(let e of i.declarations)for(let t of eG(e.id))n(t)}(n,!0,t)},e.walkFunctionParams=function(e,t){for(let n of e.params)for(let e of eG(n))t(e)},e.walkIdentifiers=function(e,t,n=!1,i=[],s=/* @__PURE__ */Object.create(null)){},e.warnDeprecation=function(e,t,n,...i){if("suppress-warning"===eX(e,t))return;let{message:s,link:r}=ek[e],o=SyntaxError(`(deprecation ${e}) ${"function"==typeof s?s(...i):s}${r?`
  Details: ${r}`:""}`);o.code=e,n&&(o.loc=n),t.onWarn(o)},e}({});
