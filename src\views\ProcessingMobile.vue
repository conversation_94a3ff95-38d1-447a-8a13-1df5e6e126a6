<template>
  <div class="processing-mobile">
    <header class="mobile-header">
      <h1 class="header-title">加工增值</h1>
      <div class="header-subtitle">海南海关领导视窗</div>
    </header>

    <main class="mobile-main">
      <!-- 概览卡片 -->
      <section class="overview-section">
        <div class="card-grid">
          <DataCard
            v-for="card in overviewCards"
            :key="card.key"
            :title="card.title"
            :value="card.value"
            :unit="card.unit"
            :growth="card.growth"
            :loading="loading"
            device-type="mobile"
          />
        </div>
      </section>

      <!-- 图表区域 -->
      <section class="charts-section">
        <div class="chart-item">
          <ChartContainer
            :config="monthlyTrendChart"
            :loading="loading"
            :error="error"
            device-type="mobile"
            @retry="loadData"
          />
        </div>

        <div class="chart-item">
          <ChartContainer
            :config="companyTypesChart"
            :loading="loading"
            :error="error"
            device-type="mobile"
            @retry="loadData"
          />
        </div>

        <div class="chart-item">
          <ChartContainer
            :config="topCompaniesChart"
            :loading="loading"
            :error="error"
            device-type="mobile"
            @retry="loadData"
          />
        </div>

        <div class="chart-item">
          <ChartContainer
            :config="topProductsChart"
            :loading="loading"
            :error="error"
            device-type="mobile"
            @retry="loadData"
          />
        </div>
      </section>
    </main>
  </div>
</template>

<script>
import DataCard from '../components/DataCard.vue';
import ChartContainer from '../components/ChartContainer.vue';
import chartMiddleware from '../middleware/chartMiddleware.js';

export default {
  name: 'ProcessingMobile',
  components: {
    DataCard,
    ChartContainer
  },
  data() {
    return {
      loading: true,
      error: '',
      overviewData: null,
      monthlyTrendChart: {},
      companyTypesChart: {},
      topCompaniesChart: {},
      topProductsChart: {}
    };
  },
  computed: {
    overviewCards() {
      if (!this.overviewData) return [];
      
      return [
        {
          key: 'totalValue',
          title: this.overviewData.totalValue.title,
          value: this.overviewData.totalValue.value,
          unit: this.overviewData.totalValue.unit,
          growth: this.overviewData.totalValue.growth
        },
        {
          key: 'totalVolume',
          title: this.overviewData.totalVolume.title,
          value: this.overviewData.totalVolume.value,
          unit: this.overviewData.totalVolume.unit,
          growth: this.overviewData.totalVolume.growth
        },
        {
          key: 'exemptTax',
          title: this.overviewData.exemptTax.title,
          value: this.overviewData.exemptTax.value,
          unit: this.overviewData.exemptTax.unit,
          growth: this.overviewData.exemptTax.growth
        },
        {
          key: 'registeredCompanies',
          title: this.overviewData.registeredCompanies.title,
          value: this.overviewData.registeredCompanies.value,
          unit: this.overviewData.registeredCompanies.unit,
          growth: this.overviewData.registeredCompanies.growth
        }
      ];
    }
  },
  async mounted() {
    await this.loadData();
  },
  methods: {
    async loadData() {
      this.loading = true;
      this.error = '';
      
      try {
        // 获取概览数据
        this.overviewData = await chartMiddleware.getOverviewData('processing');
        
        // 获取图表数据
        const [monthlyTrend, companyTypes, topCompanies, topProducts] = await Promise.all([
          chartMiddleware.getProcessingChartData('monthlyTrend', 'mobile'),
          chartMiddleware.getProcessingChartData('companyTypes', 'mobile'),
          chartMiddleware.getProcessingChartData('topCompanies', 'mobile'),
          chartMiddleware.getProcessingChartData('topProducts', 'mobile')
        ]);
        
        this.monthlyTrendChart = monthlyTrend;
        this.companyTypesChart = companyTypes;
        this.topCompaniesChart = topCompanies;
        this.topProductsChart = topProducts;
        
      } catch (error) {
        console.error('加载数据失败:', error);
        this.error = '数据加载失败，请重试';
      } finally {
        this.loading = false;
      }
    }
  }
};
</script>

<style scoped>
.processing-mobile {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding-bottom: 20px;
}

.mobile-header {
  background: linear-gradient(135deg, #1a5276 0%, #2874a6 100%);
  color: white;
  padding: 20px 16px;
  text-align: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header-title {
  font-size: 24px;
  font-weight: bold;
  margin: 0 0 8px 0;
}

.header-subtitle {
  font-size: 14px;
  opacity: 0.9;
  margin: 0;
}

.mobile-main {
  padding: 16px;
}

.overview-section {
  margin-bottom: 20px;
}

.card-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.charts-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.chart-item {
  width: 100%;
}

/* 响应式调整 */
@media (max-width: 480px) {
  .mobile-header {
    padding: 16px 12px;
  }
  
  .header-title {
    font-size: 20px;
  }
  
  .mobile-main {
    padding: 12px;
  }
  
  .card-grid {
    gap: 8px;
  }
  
  .charts-section {
    gap: 12px;
  }
}

@media (max-width: 360px) {
  .card-grid {
    grid-template-columns: 1fr;
  }
}
</style>
