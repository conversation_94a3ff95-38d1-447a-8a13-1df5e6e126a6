console.log('Starting test server...');

const http = require('http');

const server = http.createServer((req, res) => {
  console.log('Request received:', req.url);
  res.writeHead(200, { 'Content-Type': 'text/html' });
  res.end('<h1>Test Server Working!</h1>');
});

server.listen(8080, () => {
  console.log('Test server running on http://localhost:8080');
});

server.on('error', (err) => {
  console.error('Server error:', err);
});
