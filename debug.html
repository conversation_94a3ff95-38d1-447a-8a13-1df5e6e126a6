<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试页面</title>
    <link rel="stylesheet" href="/src/styles.css">
</head>
<body>
    <div id="app">
        <div style="padding: 20px; background: white; margin: 20px; border-radius: 8px;">
            <h1>海南海关领导视窗系统 - 调试模式</h1>
            <div id="status">正在初始化...</div>
            <div id="chart1" style="width: 100%; height: 400px; background: #f0f0f0; margin: 20px 0; border: 1px solid #ccc;">
                图表区域
            </div>
        </div>
    </div>
    
    <!-- ECharts库 -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    
    <script>
        console.log('🚀 调试页面开始执行');
        
        function updateStatus(message) {
            const status = document.getElementById('status');
            if (status) {
                status.innerHTML += '<br>' + message;
            }
            console.log(message);
        }
        
        // 检查ECharts
        if (typeof echarts !== 'undefined') {
            updateStatus('✅ ECharts已加载');
            
            // 创建测试图表
            const chartElement = document.getElementById('chart1');
            if (chartElement) {
                try {
                    const chart = echarts.init(chartElement);
                    const option = {
                        title: { 
                            text: '海南海关数据测试',
                            left: 'center',
                            textStyle: { color: '#1e293b', fontSize: 18 }
                        },
                        tooltip: { trigger: 'axis' },
                        xAxis: { 
                            type: 'category', 
                            data: ['1月', '2月', '3月', '4月', '5月', '6月'] 
                        },
                        yAxis: { type: 'value' },
                        series: [{
                            name: '货物总值',
                            type: 'line',
                            data: [8.5, 7.8, 9.2, 10.1, 11.5, 12.3],
                            smooth: true,
                            itemStyle: { color: '#3b82f6' }
                        }]
                    };
                    chart.setOption(option);
                    updateStatus('✅ 测试图表创建成功');
                } catch (error) {
                    updateStatus('❌ 图表创建失败: ' + error.message);
                }
            } else {
                updateStatus('❌ 找不到图表容器');
            }
        } else {
            updateStatus('❌ ECharts未加载');
        }
        
        updateStatus('🎉 调试页面执行完成');
    </script>
</body>
</html>
