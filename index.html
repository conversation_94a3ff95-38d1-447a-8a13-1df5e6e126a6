<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>海南海关领导视窗系统</title>
    <link rel="stylesheet" href="/src/styles.css">
</head>
<body>
    <div id="app">
        <div style="display: flex; justify-content: center; align-items: center; height: 100vh; font-size: 18px; color: #64748b;">
            系统加载中...
        </div>
    </div>

    <!-- ECharts库 -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>

    <!-- 数据层 -->
    <script src="/src/data/simulationData.js"></script>

    <!-- 中间件层 -->
    <script src="/src/middleware/dataMiddleware.js"></script>
    <script src="/src/middleware/chartConfig.js"></script>

    <!-- 应用层 -->
    <script>
        // 等待所有脚本加载完成
        window.addEventListener('load', function() {
            console.log('🔍 检查依赖加载状态...');
            console.log('ECharts:', typeof echarts !== 'undefined' ? '✅' : '❌');
            console.log('SimulationData:', typeof SimulationData !== 'undefined' ? '✅' : '❌');
            console.log('ChartMiddleware:', typeof ChartMiddleware !== 'undefined' ? '✅' : '❌');
        });
    </script>
    <script src="/src/app.js"></script>
</body>

