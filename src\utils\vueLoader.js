// Vue组件加载器
class VueLoader {
  constructor() {
    this.cache = new Map();
  }

  // 解析Vue单文件组件
  parseVueComponent(content) {
    const templateMatch = content.match(/<template>([\s\S]*?)<\/template>/);
    const scriptMatch = content.match(/<script>([\s\S]*?)<\/script>/);
    const styleMatch = content.match(/<style[^>]*>([\s\S]*?)<\/style>/);

    const template = templateMatch ? templateMatch[1].trim() : '';
    const script = scriptMatch ? scriptMatch[1].trim() : '';
    const style = styleMatch ? styleMatch[1].trim() : '';

    return { template, script, style };
  }

  // 创建组件定义
  createComponentDefinition(parsed) {
    let componentDef = {};

    // 处理script部分
    if (parsed.script) {
      try {
        // 移除import语句并替换为全局引用
        let processedScript = parsed.script
          .replace(/import\s+.*?from\s+['"].*?['"];?\s*/g, '')
          .replace(/export\s+default\s+/, 'componentDef = ');

        // 执行脚本获取组件定义
        eval(processedScript);
      } catch (error) {
        console.error('解析组件脚本失败:', error);
        componentDef = { name: 'ErrorComponent' };
      }
    }

    // 添加模板
    if (parsed.template) {
      componentDef.template = parsed.template;
    }

    // 添加样式
    if (parsed.style) {
      this.injectStyle(parsed.style);
    }

    return componentDef;
  }

  // 注入样式
  injectStyle(css) {
    const styleId = 'vue-component-style-' + Date.now();
    const existingStyle = document.getElementById(styleId);
    
    if (!existingStyle) {
      const style = document.createElement('style');
      style.id = styleId;
      style.textContent = css;
      document.head.appendChild(style);
    }
  }

  // 加载组件
  async loadComponent(path) {
    if (this.cache.has(path)) {
      return this.cache.get(path);
    }

    try {
      const response = await fetch(path);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const content = await response.text();
      const parsed = this.parseVueComponent(content);
      const componentDef = this.createComponentDefinition(parsed);
      
      this.cache.set(path, componentDef);
      return componentDef;
    } catch (error) {
      console.error('加载组件失败:', error);
      return {
        name: 'ErrorComponent',
        template: `
          <div style="display: flex; justify-content: center; align-items: center; height: 100vh; flex-direction: column; color: #666;">
            <div style="font-size: 24px; margin-bottom: 16px;">⚠</div>
            <div>组件加载失败: ${path}</div>
            <div style="font-size: 12px; margin-top: 8px; color: #999;">${error.message}</div>
          </div>
        `
      };
    }
  }
}

// 创建全局实例
window.vueLoader = new VueLoader();

export default window.vueLoader;
