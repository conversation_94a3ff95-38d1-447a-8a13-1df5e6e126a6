/**
* @vue/reactivity v3.5.0
* (c) 2018-present <PERSON><PERSON> (<PERSON>) <PERSON> and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */let e,t,i,s;let r={},n=()=>{},l=Object.assign,a=(e,t)=>{let i=e.indexOf(t);i>-1&&e.splice(i,1)},o=Object.prototype.hasOwnProperty,u=(e,t)=>o.call(e,t),f=Array.isArray,c=e=>"[object Map]"===y(e),h=e=>"[object Set]"===y(e),p=e=>"function"==typeof e,d=e=>"string"==typeof e,v=e=>"symbol"==typeof e,_=e=>null!==e&&"object"==typeof e,g=Object.prototype.toString,y=e=>g.call(e),R=e=>y(e).slice(8,-1),b=e=>"[object Object]"===y(e),w=e=>d(e)&&"NaN"!==e&&"-"!==e[0]&&""+parseInt(e,10)===e,S=(e,t)=>!Object.is(e,t),E=(e,t,i,s=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:s,value:i})};class x{constructor(t=!1){this.detached=t,this._active=!0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=e,!t&&e&&(this.index=(e.scopes||(e.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){let e,t;if(this._isPaused=!0,this.scopes)for(e=0,t=this.scopes.length;e<t;e++)this.scopes[e].pause();for(e=0,t=this.effects.length;e<t;e++)this.effects[e].pause()}}resume(){if(this._active&&this._isPaused){let e,t;if(this._isPaused=!1,this.scopes)for(e=0,t=this.scopes.length;e<t;e++)this.scopes[e].resume();for(e=0,t=this.effects.length;e<t;e++)this.effects[e].resume()}}run(t){if(this._active){let i=e;try{return e=this,t()}finally{e=i}}}on(){e=this}off(){e=this.parent}stop(e){if(this._active){let t,i;for(t=0,i=this.effects.length;t<i;t++)this.effects[t].stop();for(t=0,i=this.cleanups.length;t<i;t++)this.cleanups[t]();if(this.scopes)for(t=0,i=this.scopes.length;t<i;t++)this.scopes[t].stop(!0);if(!this.detached&&this.parent&&!e){let e=this.parent.scopes.pop();e&&e!==this&&(this.parent.scopes[this.index]=e,e.index=this.index)}this.parent=void 0,this._active=!1}}}function T(e){return new x(e)}function A(){return e}function m(t,i=!1){e&&e.cleanups.push(t)}let k={ACTIVE:1,1:"ACTIVE",RUNNING:2,2:"RUNNING",TRACKING:4,4:"TRACKING",NOTIFIED:8,8:"NOTIFIED",DIRTY:16,16:"DIRTY",ALLOW_RECURSE:32,32:"ALLOW_RECURSE",PAUSED:64,64:"PAUSED"},D=/* @__PURE__ */new WeakSet;class O{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.nextEffect=void 0,this.cleanup=void 0,this.scheduler=void 0,e&&e.active&&e.effects.push(this)}pause(){this.flags|=64}resume(){64&this.flags&&(this.flags&=-65,D.has(this)&&(D.delete(this),this.trigger()))}notify(){(!(2&this.flags)||32&this.flags)&&(8&this.flags||(this.flags|=8,this.nextEffect=i,i=this))}run(){if(!(1&this.flags))return this.fn();this.flags|=2,F(this),C(this);let e=t,i=M;t=this,M=!0;try{return this.fn()}finally{P(this),t=e,M=i,this.flags&=-3}}stop(){if(1&this.flags){for(let e=this.deps;e;e=e.nextDep)N(e);this.deps=this.depsTail=void 0,F(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){64&this.flags?D.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){j(this)&&this.run()}get dirty(){return j(this)}}let I=0;function L(){let e;if(!(--I>0)){for(;i;){let t=i;for(i=void 0;t;){let i=t.nextEffect;if(t.nextEffect=void 0,t.flags&=-9,1&t.flags)try{t.trigger()}catch(t){e||(e=t)}t=i}}if(e)throw e}}function C(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function P(e){let t;let i=e.depsTail;for(let e=i;e;e=e.prevDep)-1===e.version?(e===i&&(i=e.prevDep),N(e),function(e){let{prevDep:t,nextDep:i}=e;t&&(t.nextDep=i,e.prevDep=void 0),i&&(i.prevDep=t,e.nextDep=void 0)}(e)):t=e,e.dep.activeLink=e.prevActiveLink,e.prevActiveLink=void 0;e.deps=t,e.depsTail=i}function j(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&!1===W(t.dep.computed)||t.dep.version!==t.version)return!0;return!!e._dirty}function W(e){if(2&e.flags)return!1;if(4&e.flags&&!(16&e.flags)||(e.flags&=-17,e.globalVersion===B))return;e.globalVersion=B;let i=e.dep;if(e.flags|=2,i.version>0&&!e.isSSR&&!j(e)){e.flags&=-3;return}let s=t,r=M;t=e,M=!0;try{C(e);let t=e.fn();(0===i.version||S(t,e._value))&&(e._value=t,i.version++)}catch(e){throw i.version++,e}finally{t=s,M=r,P(e),e.flags&=-3}}function N(e){let{dep:t,prevSub:i,nextSub:s}=e;if(i&&(i.nextSub=s,e.prevSub=void 0),s&&(s.prevSub=i,e.nextSub=void 0),t.subs===e&&(t.subs=i),!t.subs&&t.computed){t.computed.flags&=-5;for(let e=t.computed.deps;e;e=e.nextDep)N(e)}}function V(e,t){e.effect instanceof O&&(e=e.effect.fn);let i=new O(e);t&&l(i,t);try{i.run()}catch(e){throw i.stop(),e}let s=i.run.bind(i);return s.effect=i,s}function K(e){e.effect.stop()}let M=!0,H=[];function U(){H.push(M),M=!1}function Y(){H.push(M),M=!0}function G(){let e=H.pop();M=void 0===e||e}function z(e,i=!1){t instanceof O&&(t.cleanup=e)}function F(e){let{cleanup:i}=e;if(e.cleanup=void 0,i){let e=t;t=void 0;try{i()}finally{t=e}}}let B=0;class q{constructor(e){this.computed=e,this.version=0,this.activeLink=void 0,this.subs=void 0}track(e){if(!t||!M)return;let i=this.activeLink;if(void 0===i||i.sub!==t)i=this.activeLink={dep:this,sub:t,version:this.version,nextDep:void 0,prevDep:void 0,nextSub:void 0,prevSub:void 0,prevActiveLink:void 0},t.deps?(i.prevDep=t.depsTail,t.depsTail.nextDep=i,t.depsTail=i):t.deps=t.depsTail=i,4&t.flags&&function e(t){let i=t.dep.computed;if(i&&!t.dep.subs){i.flags|=20;for(let t=i.deps;t;t=t.nextDep)e(t)}let s=t.dep.subs;s!==t&&(t.prevSub=s,s&&(s.nextSub=t)),t.dep.subs=t}(i);else if(-1===i.version&&(i.version=this.version,i.nextDep)){let e=i.nextDep;e.prevDep=i.prevDep,i.prevDep&&(i.prevDep.nextDep=e),i.prevDep=t.depsTail,i.nextDep=void 0,t.depsTail.nextDep=i,t.depsTail=i,t.deps===i&&(t.deps=e)}return i}trigger(e){this.version++,B++,this.notify(e)}notify(e){I++;try{for(let e=this.subs;e;e=e.prevSub)e.sub.notify()}finally{L()}}}let J=/* @__PURE__ */new WeakMap,Q=Symbol(""),X=Symbol(""),Z=Symbol("");function $(e,i,s){if(M&&t){let t=J.get(e);t||J.set(e,t=/* @__PURE__ */new Map);let i=t.get(s);i||t.set(s,i=new q),i.track()}}function ee(e,t,i,s,r,n){let l=J.get(e);if(!l){B++;return}let a=[];if("clear"===t)a=[...l.values()];else{let r=f(e),n=r&&w(i);if(r&&"length"===i){let e=Number(s);l.forEach((t,i)=>{("length"===i||i===Z||!v(i)&&i>=e)&&a.push(t)})}else{let s=e=>e&&a.push(e);switch(void 0!==i&&s(l.get(i)),n&&s(l.get(Z)),t){case"add":r?n&&s(l.get("length")):(s(l.get(Q)),c(e)&&s(l.get(X)));break;case"delete":!r&&(s(l.get(Q)),c(e)&&s(l.get(X)));break;case"set":c(e)&&s(l.get(Q))}}}for(let e of(I++,a))e.trigger();L()}function et(e){let t=e0(e);return t===e?t:($(t,"iterate",Z),eZ(e)?t:t.map(e2))}function ei(e){return $(e=e0(e),"iterate",Z),e}let es={__proto__:null,[Symbol.iterator](){return er(this,Symbol.iterator,e2)},concat(...e){return et(this).concat(...e.map(e=>et(e)))},entries(){return er(this,"entries",e=>(e[1]=e2(e[1]),e))},every(e,t){return el(this,"every",e,t,void 0,arguments)},filter(e,t){return el(this,"filter",e,t,e=>e.map(e2),arguments)},find(e,t){return el(this,"find",e,t,e2,arguments)},findIndex(e,t){return el(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return el(this,"findLast",e,t,e2,arguments)},findLastIndex(e,t){return el(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return el(this,"forEach",e,t,void 0,arguments)},includes(...e){return eo(this,"includes",e)},indexOf(...e){return eo(this,"indexOf",e)},join(e){return et(this).join(e)},lastIndexOf(...e){return eo(this,"lastIndexOf",e)},map(e,t){return el(this,"map",e,t,void 0,arguments)},pop(){return eu(this,"pop")},push(...e){return eu(this,"push",e)},reduce(e,...t){return ea(this,"reduce",e,t)},reduceRight(e,...t){return ea(this,"reduceRight",e,t)},shift(){return eu(this,"shift")},some(e,t){return el(this,"some",e,t,void 0,arguments)},splice(...e){return eu(this,"splice",e)},toReversed(){return et(this).toReversed()},toSorted(e){return et(this).toSorted(e)},toSpliced(...e){return et(this).toSpliced(...e)},unshift(...e){return eu(this,"unshift",e)},values(){return er(this,"values",e2)}};function er(e,t,i){let s=ei(e),r=s[t]();return s===e||eZ(e)||(r._next=r.next,r.next=()=>{let e=r._next();return e.value&&(e.value=i(e.value)),e}),r}let en=Array.prototype;function el(e,t,i,s,r,n){let l=ei(e),a=l!==e&&!eZ(e),o=l[t];if(o!==en[t]){let t=o.apply(e,n);return a?e2(t):t}let u=i;l!==e&&(a?u=function(t,s){return i.call(this,e2(t),s,e)}:i.length>2&&(u=function(t,s){return i.call(this,t,s,e)}));let f=o.call(l,u,s);return a&&r?r(f):f}function ea(e,t,i,s){let r=ei(e),n=i;return r!==e&&(eZ(e)?i.length>3&&(n=function(t,s,r){return i.call(this,t,s,r,e)}):n=function(t,s,r){return i.call(this,t,e2(s),r,e)}),r[t](n,...s)}function eo(e,t,i){let s=e0(e);$(s,"iterate",Z);let r=s[t](...i);return(-1===r||!1===r)&&e$(i[0])?(i[0]=e0(i[0]),s[t](...i)):r}function eu(e,t,i=[]){U(),I++;let s=e0(e)[t].apply(e,i);return L(),G(),s}let ef=/* @__PURE__ */function(e,t){let i=new Set(e.split(","));return e=>i.has(e)}("__proto__,__v_isRef,__isVue"),ec=new Set(/* @__PURE__ */Object.getOwnPropertyNames(Symbol).filter(e=>"arguments"!==e&&"caller"!==e).map(e=>Symbol[e]).filter(v));function eh(e){v(e)||(e=String(e));let t=e0(this);return $(t,"has",e),t.hasOwnProperty(e)}class ep{constructor(e=!1,t=!1){this._isReadonly=e,this._isShallow=t}get(e,t,i){let s=this._isReadonly,r=this._isShallow;if("__v_isReactive"===t)return!s;if("__v_isReadonly"===t)return s;if("__v_isShallow"===t)return r;if("__v_raw"===t)return i===(s?r?eG:eY:r?eU:eH).get(e)||Object.getPrototypeOf(e)===Object.getPrototypeOf(i)?e:void 0;let n=f(e);if(!s){let e;if(n&&(e=es[t]))return e;if("hasOwnProperty"===t)return eh}let l=Reflect.get(e,t,e3(e)?e:i);return(v(t)?ec.has(t):ef(t))?l:(s||$(e,"get",t),r)?l:e3(l)?n&&w(t)?l:l.value:_(l)?s?eB(l):ez(l):l}}class ed extends ep{constructor(e=!1){super(!1,e)}set(e,t,i,s){let r=e[t];if(!this._isShallow){let t=eX(r);if(eZ(i)||eX(i)||(r=e0(r),i=e0(i)),!f(e)&&e3(r)&&!e3(i))return!t&&(r.value=i,!0)}let n=f(e)&&w(t)?Number(t)<e.length:u(e,t),l=Reflect.set(e,t,i,e3(e)?e:s);return e===e0(s)&&(n?S(i,r)&&ee(e,"set",t,i):ee(e,"add",t,i)),l}deleteProperty(e,t){let i=u(e,t);e[t];let s=Reflect.deleteProperty(e,t);return s&&i&&ee(e,"delete",t,void 0),s}has(e,t){let i=Reflect.has(e,t);return v(t)&&ec.has(t)||$(e,"has",t),i}ownKeys(e){return $(e,"iterate",f(e)?"length":Q),Reflect.ownKeys(e)}}class ev extends ep{constructor(e=!1){super(!0,e)}set(e,t){return!0}deleteProperty(e,t){return!0}}let e_=/* @__PURE__ */new ed,eg=/* @__PURE__ */new ev,ey=/* @__PURE__ */new ed(!0),eR=/* @__PURE__ */new ev(!0),eb=e=>e,ew=e=>Reflect.getPrototypeOf(e);function eS(e,t,i=!1,s=!1){let r=e0(e=e.__v_raw),n=e0(t);i||(S(t,n)&&$(r,"get",t),$(r,"get",n));let{has:l}=ew(r),a=s?eb:i?e4:e2;return l.call(r,t)?a(e.get(t)):l.call(r,n)?a(e.get(n)):void(e!==r&&e.get(t))}function eE(e,t=!1){let i=this.__v_raw,s=e0(i),r=e0(e);return t||(S(e,r)&&$(s,"has",e),$(s,"has",r)),e===r?i.has(e):i.has(e)||i.has(r)}function ex(e,t=!1){return e=e.__v_raw,t||$(e0(e),"iterate",Q),Reflect.get(e,"size",e)}function eT(e,t=!1){t||eZ(e)||eX(e)||(e=e0(e));let i=e0(this);return ew(i).has.call(i,e)||(i.add(e),ee(i,"add",e,e)),this}function eA(e,t,i=!1){i||eZ(t)||eX(t)||(t=e0(t));let s=e0(this),{has:r,get:n}=ew(s),l=r.call(s,e);l||(e=e0(e),l=r.call(s,e));let a=n.call(s,e);return s.set(e,t),l?S(t,a)&&ee(s,"set",e,t):ee(s,"add",e,t),this}function em(e){let t=e0(this),{has:i,get:s}=ew(t),r=i.call(t,e);r||(e=e0(e),r=i.call(t,e)),s&&s.call(t,e);let n=t.delete(e);return r&&ee(t,"delete",e,void 0),n}function ek(){let e=e0(this),t=0!==e.size,i=e.clear();return t&&ee(e,"clear",void 0,void 0),i}function eD(e,t){return function(i,s){let r=this,n=r.__v_raw,l=e0(n),a=t?eb:e?e4:e2;return e||$(l,"iterate",Q),n.forEach((e,t)=>i.call(s,a(e),a(t),r))}}function eO(e,t,i){return function(...s){let r=this.__v_raw,n=e0(r),l=c(n),a="entries"===e||e===Symbol.iterator&&l,o=r[e](...s),u=i?eb:t?e4:e2;return t||$(n,"iterate","keys"===e&&l?X:Q),{next(){let{value:e,done:t}=o.next();return t?{value:e,done:t}:{value:a?[u(e[0]),u(e[1])]:u(e),done:t}},[Symbol.iterator](){return this}}}}function eI(e){return function(...t){return"delete"!==e&&("clear"===e?void 0:this)}}let[eL,eC,eP,ej]=/* @__PURE__ */function(){let e={get(e){return eS(this,e)},get size(){return ex(this)},has:eE,add:eT,set:eA,delete:em,clear:ek,forEach:eD(!1,!1)},t={get(e){return eS(this,e,!1,!0)},get size(){return ex(this)},has:eE,add(e){return eT.call(this,e,!0)},set(e,t){return eA.call(this,e,t,!0)},delete:em,clear:ek,forEach:eD(!1,!0)},i={get(e){return eS(this,e,!0)},get size(){return ex(this,!0)},has(e){return eE.call(this,e,!0)},add:eI("add"),set:eI("set"),delete:eI("delete"),clear:eI("clear"),forEach:eD(!0,!1)},s={get(e){return eS(this,e,!0,!0)},get size(){return ex(this,!0)},has(e){return eE.call(this,e,!0)},add:eI("add"),set:eI("set"),delete:eI("delete"),clear:eI("clear"),forEach:eD(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach(r=>{e[r]=eO(r,!1,!1),i[r]=eO(r,!0,!1),t[r]=eO(r,!1,!0),s[r]=eO(r,!0,!0)}),[e,i,t,s]}();function eW(e,t){let i=t?e?ej:eP:e?eC:eL;return(t,s,r)=>"__v_isReactive"===s?!e:"__v_isReadonly"===s?e:"__v_raw"===s?t:Reflect.get(u(i,s)&&s in t?i:t,s,r)}let eN={get:/* @__PURE__ */eW(!1,!1)},eV={get:/* @__PURE__ */eW(!1,!0)},eK={get:/* @__PURE__ */eW(!0,!1)},eM={get:/* @__PURE__ */eW(!0,!0)},eH=/* @__PURE__ */new WeakMap,eU=/* @__PURE__ */new WeakMap,eY=/* @__PURE__ */new WeakMap,eG=/* @__PURE__ */new WeakMap;function ez(e){return eX(e)?e:eJ(e,!1,e_,eN,eH)}function eF(e){return eJ(e,!1,ey,eV,eU)}function eB(e){return eJ(e,!0,eg,eK,eY)}function eq(e){return eJ(e,!0,eR,eM,eG)}function eJ(e,t,i,s,r){if(!_(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;let n=r.get(e);if(n)return n;let l=e.__v_skip||!Object.isExtensible(e)?0:function(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}(R(e));if(0===l)return e;let a=new Proxy(e,2===l?s:i);return r.set(e,a),a}function eQ(e){return eX(e)?eQ(e.__v_raw):!!(e&&e.__v_isReactive)}function eX(e){return!!(e&&e.__v_isReadonly)}function eZ(e){return!!(e&&e.__v_isShallow)}function e$(e){return!!e&&!!e.__v_raw}function e0(e){let t=e&&e.__v_raw;return t?e0(t):e}function e1(e){return Object.isExtensible(e)&&E(e,"__v_skip",!0),e}let e2=e=>_(e)?ez(e):e,e4=e=>_(e)?eB(e):e;function e3(e){return!!e&&!0===e.__v_isRef}function e6(e){return e5(e,!1)}function e8(e){return e5(e,!0)}function e5(e,t){return e3(e)?e:new e7(e,t)}class e7{constructor(e,t){this.dep=new q,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=t?e:e0(e),this._value=t?e:e2(e),this.__v_isShallow=t}get value(){return this.dep.track(),this._value}set value(e){let t=this._rawValue,i=this.__v_isShallow||eZ(e)||eX(e);S(e=i?e:e0(e),t)&&(this._rawValue=e,this._value=i?e:e2(e),this.dep.trigger())}}function e9(e){e.dep.trigger()}function te(e){return e3(e)?e.value:e}function tt(e){return p(e)?e():te(e)}let ti={get:(e,t,i)=>te(Reflect.get(e,t,i)),set:(e,t,i,s)=>{let r=e[t];return e3(r)&&!e3(i)?(r.value=i,!0):Reflect.set(e,t,i,s)}};function ts(e){return eQ(e)?e:new Proxy(e,ti)}class tr{constructor(e){this.__v_isRef=!0,this._value=void 0;let t=this.dep=new q,{get:i,set:s}=e(t.track.bind(t),t.trigger.bind(t));this._get=i,this._set=s}get value(){return this._value=this._get()}set value(e){this._set(e)}}function tn(e){return new tr(e)}function tl(e){let t=f(e)?Array(e.length):{};for(let i in e)t[i]=tf(e,i);return t}class ta{constructor(e,t,i){this._object=e,this._key=t,this._defaultValue=i,this.__v_isRef=!0,this._value=void 0}get value(){let e=this._object[this._key];return this._value=void 0===e?this._defaultValue:e}set value(e){this._object[this._key]=e}get dep(){var e,t,i;return e=e0(this._object),t=this._key,null==(i=J.get(e))?void 0:i.get(t)}}class to{constructor(e){this._getter=e,this.__v_isRef=!0,this.__v_isReadonly=!0,this._value=void 0}get value(){return this._value=this._getter()}}function tu(e,t,i){return e3(e)?e:p(e)?new to(e):_(e)&&arguments.length>1?tf(e,t,i):e6(e)}function tf(e,t,i){let s=e[t];return e3(s)?s:new ta(e,t,i)}class tc{constructor(e,t,i){this.fn=e,this.setter=t,this._value=void 0,this.dep=new q(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=B-1,this.effect=this,this.__v_isReadonly=!t,this.isSSR=i}notify(){t!==this&&(this.flags|=16,this.dep.notify())}get value(){let e=this.dep.track();return W(this),e&&(e.version=this.dep.version),this._value}set value(e){this.setter&&this.setter(e)}}function th(e,t,i=!1){let s,r;return p(e)?s=e:(s=e.get,r=e.set),new tc(s,r,i)}let tp={GET:"get",HAS:"has",ITERATE:"iterate"},td={SET:"set",ADD:"add",DELETE:"delete",CLEAR:"clear"},tv={SKIP:"__v_skip",IS_REACTIVE:"__v_isReactive",IS_READONLY:"__v_isReadonly",IS_SHALLOW:"__v_isShallow",RAW:"__v_raw",IS_REF:"__v_isRef"},t_={WATCH_GETTER:2,2:"WATCH_GETTER",WATCH_CALLBACK:3,3:"WATCH_CALLBACK",WATCH_CLEANUP:4,4:"WATCH_CLEANUP"},tg={},ty=/* @__PURE__ */new WeakMap;function tR(){return s}function tb(e,t=!1,i=s){if(i){let t=ty.get(i);t||ty.set(i,t=[]),t.push(e)}}function tw(e,t,i=r){let l,o,u,c;let{immediate:h,deep:d,once:v,scheduler:_,augmentJob:g,call:y}=i,R=e=>d?e:eZ(e)||!1===d||0===d?tS(e,1):tS(e),b=!1,w=!1;if(e3(e)?(o=()=>e.value,b=eZ(e)):eQ(e)?(o=()=>R(e),b=!0):f(e)?(w=!0,b=e.some(e=>eQ(e)||eZ(e)),o=()=>e.map(e=>e3(e)?e.value:eQ(e)?R(e):p(e)?y?y(e,2):e():void 0)):o=p(e)?t?y?()=>y(e,2):e:()=>{if(u){U();try{u()}finally{G()}}let t=s;s=l;try{return y?y(e,3,[c]):e(c)}finally{s=t}}:n,t&&d){let e=o,t=!0===d?1/0:d;o=()=>tS(e(),t)}let E=A(),x=()=>{l.stop(),E&&a(E.effects,l)};if(v){if(t){let e=t;t=(...t)=>{e(...t),x()}}else{let e=o;o=()=>{e(),x()}}}let T=w?Array(e.length).fill(tg):tg,m=e=>{if(1&l.flags&&(l.dirty||e)){if(t){let e=l.run();if(d||b||(w?e.some((e,t)=>S(e,T[t])):S(e,T))){u&&u();let i=s;s=l;try{let i=[e,T===tg?void 0:w&&T[0]===tg?[]:T,c];y?y(t,3,i):t(...i),T=e}finally{s=i}}}else l.run()}};return g&&g(m),(l=new O(o)).scheduler=_?()=>_(m,!1):m,c=e=>tb(e,!1,l),u=l.onStop=()=>{let e=ty.get(l);if(e){if(y)y(e,4);else for(let t of e)t();ty.delete(l)}},t?h?m(!0):T=l.run():_?_(m.bind(null,!0),!0):l.run(),x.pause=l.pause.bind(l),x.resume=l.resume.bind(l),x.stop=x,x}function tS(e,t=1/0,i){if(t<=0||!_(e)||e.__v_skip||(i=i||/* @__PURE__ */new Set).has(e))return e;if(i.add(e),t--,e3(e))tS(e.value,t,i);else if(f(e))for(let s=0;s<e.length;s++)tS(e[s],t,i);else if(h(e)||c(e))e.forEach(e=>{tS(e,t,i)});else if(b(e)){for(let s in e)tS(e[s],t,i);for(let s of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,s)&&tS(e[s],t,i)}return e}export{Z as ARRAY_ITERATE_KEY,k as EffectFlags,x as EffectScope,Q as ITERATE_KEY,X as MAP_KEY_ITERATE_KEY,O as ReactiveEffect,tv as ReactiveFlags,tp as TrackOpTypes,td as TriggerOpTypes,t_ as WatchErrorCodes,th as computed,tn as customRef,V as effect,T as effectScope,Y as enableTracking,A as getCurrentScope,tR as getCurrentWatcher,e$ as isProxy,eQ as isReactive,eX as isReadonly,e3 as isRef,eZ as isShallow,e1 as markRaw,z as onEffectCleanup,m as onScopeDispose,tb as onWatcherCleanup,U as pauseTracking,ts as proxyRefs,ez as reactive,et as reactiveReadArray,eB as readonly,e6 as ref,G as resetTracking,eF as shallowReactive,ei as shallowReadArray,eq as shallowReadonly,e8 as shallowRef,K as stop,e0 as toRaw,e2 as toReactive,e4 as toReadonly,tu as toRef,tl as toRefs,tt as toValue,$ as track,tS as traverse,ee as trigger,e9 as triggerRef,te as unref,tw as watch};
