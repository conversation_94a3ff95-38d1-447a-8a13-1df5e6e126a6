<template>
  <div class="processing-pc">
    <header class="pc-header">
      <div class="header-content">
        <h1 class="header-title">加工增值业务统计</h1>
        <div class="header-subtitle">海南海关领导视窗系统</div>
      </div>
    </header>

    <main class="pc-main">
      <!-- 概览卡片区域 -->
      <section class="overview-section">
        <div class="overview-grid">
          <DataCard
            v-for="card in overviewCards"
            :key="card.key"
            :title="card.title"
            :value="card.value"
            :unit="card.unit"
            :growth="card.growth"
            :loading="loading"
            device-type="pc"
          />
        </div>
      </section>

      <!-- 图表区域 -->
      <section class="charts-section">
        <div class="charts-row">
          <div class="chart-col-2">
            <ChartContainer
              :config="monthlyTrendChart"
              :loading="loading"
              :error="error"
              device-type="pc"
              @retry="loadData"
            />
          </div>
          <div class="chart-col-1">
            <ChartContainer
              :config="companyTypesChart"
              :loading="loading"
              :error="error"
              device-type="pc"
              @retry="loadData"
            />
          </div>
        </div>

        <div class="charts-row">
          <div class="chart-col-1">
            <ChartContainer
              :config="regionDistributionChart"
              :loading="loading"
              :error="error"
              device-type="pc"
              @retry="loadData"
            />
          </div>
          <div class="chart-col-2">
            <ChartContainer
              :config="topCompaniesChart"
              :loading="loading"
              :error="error"
              device-type="pc"
              @retry="loadData"
            />
          </div>
        </div>

        <div class="charts-row">
          <div class="chart-col-2">
            <ChartContainer
              :config="topProductsChart"
              :loading="loading"
              :error="error"
              device-type="pc"
              @retry="loadData"
            />
          </div>
          <div class="chart-col-1">
            <ChartContainer
              :config="domesticSalesChart"
              :loading="loading"
              :error="error"
              device-type="pc"
              @retry="loadData"
            />
          </div>
        </div>
      </section>
    </main>
  </div>
</template>

<script>
export default {
  name: 'ProcessingPC',
  components: {
    DataCard: window.DataCard,
    ChartContainer: window.ChartContainer
  },
  data() {
    return {
      loading: true,
      error: '',
      overviewData: null,
      monthlyTrendChart: {},
      companyTypesChart: {},
      regionDistributionChart: {},
      topCompaniesChart: {},
      topProductsChart: {},
      domesticSalesChart: {}
    };
  },
  computed: {
    overviewCards() {
      if (!this.overviewData) return [];
      
      return [
        {
          key: 'totalValue',
          title: this.overviewData.totalValue.title,
          value: this.overviewData.totalValue.value,
          unit: this.overviewData.totalValue.unit,
          growth: this.overviewData.totalValue.growth
        },
        {
          key: 'totalVolume',
          title: this.overviewData.totalVolume.title,
          value: this.overviewData.totalVolume.value,
          unit: this.overviewData.totalVolume.unit,
          growth: this.overviewData.totalVolume.growth
        },
        {
          key: 'exemptTax',
          title: this.overviewData.exemptTax.title,
          value: this.overviewData.exemptTax.value,
          unit: this.overviewData.exemptTax.unit,
          growth: this.overviewData.exemptTax.growth
        },
        {
          key: 'registeredCompanies',
          title: this.overviewData.registeredCompanies.title,
          value: this.overviewData.registeredCompanies.value,
          unit: this.overviewData.registeredCompanies.unit,
          growth: this.overviewData.registeredCompanies.growth
        }
      ];
    }
  },
  async mounted() {
    await this.loadData();
  },
  methods: {
    async loadData() {
      this.loading = true;
      this.error = '';
      
      try {
        // 获取概览数据
        this.overviewData = await chartMiddleware.getOverviewData('processing');
        
        // 获取图表数据
        const [
          monthlyTrend, 
          companyTypes, 
          regionDistribution, 
          topCompanies, 
          topProducts, 
          domesticSales
        ] = await Promise.all([
          chartMiddleware.getProcessingChartData('monthlyTrend', 'pc'),
          chartMiddleware.getProcessingChartData('companyTypes', 'pc'),
          chartMiddleware.getProcessingChartData('regionDistribution', 'pc'),
          chartMiddleware.getProcessingChartData('topCompanies', 'pc'),
          chartMiddleware.getProcessingChartData('topProducts', 'pc'),
          chartMiddleware.getProcessingChartData('domesticSales', 'pc')
        ]);
        
        this.monthlyTrendChart = monthlyTrend;
        this.companyTypesChart = companyTypes;
        this.regionDistributionChart = regionDistribution;
        this.topCompaniesChart = topCompanies;
        this.topProductsChart = topProducts;
        this.domesticSalesChart = domesticSales;
        
      } catch (error) {
        console.error('加载数据失败:', error);
        this.error = '数据加载失败，请重试';
      } finally {
        this.loading = false;
      }
    }
  }
};
</script>

<style scoped>
.processing-pc {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.pc-header {
  background: linear-gradient(135deg, #1a5276 0%, #2874a6 100%);
  color: white;
  padding: 24px 0;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.header-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 24px;
  text-align: center;
}

.header-title {
  font-size: 32px;
  font-weight: bold;
  margin: 0 0 8px 0;
}

.header-subtitle {
  font-size: 16px;
  opacity: 0.9;
  margin: 0;
}

.pc-main {
  max-width: 1400px;
  margin: 0 auto;
  padding: 24px;
}

.overview-section {
  margin-bottom: 24px;
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
}

.charts-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.charts-row {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 20px;
  align-items: start;
}

.charts-row:nth-child(even) {
  grid-template-columns: 1fr 2fr;
}

.chart-col-1,
.chart-col-2 {
  width: 100%;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .overview-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .charts-row {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .charts-row:nth-child(even) {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .pc-header {
    padding: 20px 0;
  }
  
  .header-content {
    padding: 0 16px;
  }
  
  .header-title {
    font-size: 24px;
  }
  
  .header-subtitle {
    font-size: 14px;
  }
  
  .pc-main {
    padding: 16px;
  }
  
  .overview-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .charts-section {
    gap: 16px;
  }
}
</style>
