// API服务层 - 统一管理所有API调用
class ApiService {
  constructor() {
    this.baseURL = 'http://localhost:3001/api';
    this.cache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5分钟缓存
  }

  // 通用HTTP请求方法
  async request(url, options = {}) {
    const cacheKey = `${url}_${JSON.stringify(options)}`;
    
    // 检查缓存
    if (this.cache.has(cacheKey)) {
      const cached = this.cache.get(cacheKey);
      if (Date.now() - cached.timestamp < this.cacheTimeout) {
        return cached.data;
      }
      this.cache.delete(cacheKey);
    }

    try {
      const response = await fetch(`${this.baseURL}${url}`, {
        method: options.method || 'GET',
        headers: {
          'Content-Type': 'application/json',
          ...options.headers
        },
        body: options.body ? JSON.stringify(options.body) : undefined
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      // 缓存成功的响应
      this.cache.set(cacheKey, {
        data,
        timestamp: Date.now()
      });

      return data;
    } catch (error) {
      console.error('API请求失败:', error);
      throw error;
    }
  }

  // 加工增值相关API
  async getProcessingOverview() {
    return this.request('/processing/overview');
  }

  async getProcessingTrend(timeRange = '12months') {
    return this.request(`/processing/trend?range=${timeRange}`);
  }

  async getProcessingCompanies(type = 'all') {
    return this.request(`/processing/companies?type=${type}`);
  }

  async getProcessingProducts(limit = 10) {
    return this.request(`/processing/products?limit=${limit}`);
  }

  // 交通工具相关API
  async getTransportOverview() {
    return this.request('/transport/overview');
  }

  async getTransportTrend(timeRange = '12months') {
    return this.request(`/transport/trend?range=${timeRange}`);
  }

  async getTransportCompanies(type = 'all') {
    return this.request(`/transport/companies?type=${type}`);
  }

  async getTransportVehicles(category = 'all') {
    return this.request(`/transport/vehicles?category=${category}`);
  }

  // 清除缓存
  clearCache() {
    this.cache.clear();
  }

  // 清除过期缓存
  clearExpiredCache() {
    const now = Date.now();
    for (const [key, value] of this.cache.entries()) {
      if (now - value.timestamp >= this.cacheTimeout) {
        this.cache.delete(key);
      }
    }
  }
}

// 创建单例实例
const apiService = new ApiService();

// 定期清理过期缓存
setInterval(() => {
  apiService.clearExpiredCache();
}, 60000); // 每分钟清理一次

export default apiService;
