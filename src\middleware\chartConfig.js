// 海南海关领导视窗系统 - 完整图表中间件
console.log('🔧 初始化完整图表中间件...');

const ChartMiddleware = {
  // 设备类型样式配置
  getDeviceStyles(deviceType) {
    const styles = {
      pc: {
        titleFontSize: 18,
        titleColor: '#1e293b',
        backgroundColor: '#ffffff',
        gridLeft: '10%',
        gridRight: '10%',
        gridTop: '15%',
        gridBottom: '15%'
      },
      mobile: {
        titleFontSize: 16,
        titleColor: '#1e293b',
        backgroundColor: '#ffffff',
        gridLeft: '8%',
        gridRight: '8%',
        gridTop: '12%',
        gridBottom: '12%'
      },
      wall: {
        titleFontSize: 28,
        titleColor: '#00d4ff',
        backgroundColor: 'rgba(0, 20, 40, 0.9)',
        gridLeft: '8%',
        gridRight: '8%',
        gridTop: '18%',
        gridBottom: '18%'
      }
    };
    return styles[deviceType] || styles.pc;
  },

  // 创建基础配置
  createBaseConfig(chartData, deviceType = 'pc') {
    const styles = this.getDeviceStyles(deviceType);
    
    return {
      backgroundColor: styles.backgroundColor,
      title: {
        text: chartData.title || '图表',
        left: 'center',
        textStyle: {
          color: styles.titleColor,
          fontSize: styles.titleFontSize,
          fontWeight: 'bold'
        }
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: deviceType === 'wall' ? 'rgba(0, 20, 40, 0.9)' : 'rgba(255, 255, 255, 0.95)',
        borderColor: deviceType === 'wall' ? '#00d4ff' : '#3b82f6',
        borderWidth: 1,
        textStyle: { 
          color: deviceType === 'wall' ? '#ffffff' : '#333333',
          fontSize: deviceType === 'wall' ? 14 : 12
        }
      },
      grid: {
        left: styles.gridLeft,
        right: styles.gridRight,
        top: styles.gridTop,
        bottom: styles.gridBottom,
        containLabel: true
      },
      legend: {
        top: '8%',
        textStyle: {
          color: styles.titleColor,
          fontSize: deviceType === 'wall' ? 16 : 12
        }
      }
    };
  },

  // 创建饼图配置
  createPieChart(chartData, deviceType = 'pc') {
    const config = this.createBaseConfig(chartData, deviceType);
    const isDoughnut = chartData.type === 'doughnut';
    
    config.tooltip = { 
      trigger: 'item', 
      formatter: '{a} <br/>{b}: {c} ({d}%)',
      backgroundColor: deviceType === 'wall' ? 'rgba(0, 20, 40, 0.9)' : 'rgba(255, 255, 255, 0.95)',
      borderColor: deviceType === 'wall' ? '#00d4ff' : '#3b82f6',
      textStyle: { 
        color: deviceType === 'wall' ? '#ffffff' : '#333333'
      }
    };
    
    config.series = [{
      name: chartData.title || '数据',
      type: 'pie',
      radius: isDoughnut ? ['50%', '80%'] : ['40%', '70%'],
      center: ['50%', '60%'],
      data: chartData.data || chartData.categoryDistribution || chartData.brandDistribution || [],
      label: { 
        show: true, 
        formatter: '{b}: {d}%',
        color: deviceType === 'wall' ? '#ffffff' : '#333333',
        fontSize: deviceType === 'wall' ? 14 : 12
      },
      labelLine: {
        lineStyle: {
          color: deviceType === 'wall' ? '#ffffff' : '#333333'
        }
      }
    }];
    
    return config;
  },

  // 创建柱状图配置
  createBarChart(chartData, deviceType = 'pc') {
    const config = this.createBaseConfig(chartData, deviceType);
    const isHorizontal = chartData.type === 'horizontalBar';
    const styles = this.getDeviceStyles(deviceType);
    
    if (isHorizontal) {
      config.xAxis = { 
        type: 'value',
        axisLabel: { color: styles.titleColor },
        axisLine: { lineStyle: { color: styles.titleColor } }
      };
      config.yAxis = { 
        type: 'category',
        data: chartData.data ? chartData.data.map(item => item.name) : 
              chartData.companyRanking ? chartData.companyRanking.map(item => item.name) : [],
        axisLabel: { color: styles.titleColor },
        axisLine: { lineStyle: { color: styles.titleColor } }
      };
    } else {
      config.xAxis = { 
        type: 'category', 
        data: chartData.data ? chartData.data.map(item => item.name) : 
              chartData.companyRanking ? chartData.companyRanking.map(item => item.name) : 
              chartData.monthlyTrend ? chartData.monthlyTrend.map(item => item.month) : [],
        axisLabel: { color: styles.titleColor },
        axisLine: { lineStyle: { color: styles.titleColor } }
      };
      config.yAxis = { 
        type: 'value',
        axisLabel: { color: styles.titleColor },
        axisLine: { lineStyle: { color: styles.titleColor } }
      };
    }
    
    config.series = [{
      type: 'bar',
      data: chartData.data ? chartData.data.map(item => item.value || item.amount) : 
            chartData.companyRanking ? chartData.companyRanking.map(item => item.amount) : 
            chartData.monthlyTrend ? chartData.monthlyTrend.map(item => item.count || item.amount || item.value) : [],
      itemStyle: {
        color: deviceType === 'wall' ? '#00d4ff' : '#3b82f6'
      }
    }];
    
    return config;
  },

  // 创建折线图配置
  createLineChart(chartData, deviceType = 'pc') {
    const config = this.createBaseConfig(chartData, deviceType);
    const styles = this.getDeviceStyles(deviceType);
    
    config.xAxis = { 
      type: 'category', 
      data: chartData.monthlyTrend ? chartData.monthlyTrend.map(item => item.month) : 
            chartData.months || chartData.categories || [],
      axisLabel: { color: styles.titleColor },
      axisLine: { lineStyle: { color: styles.titleColor } }
    };
    
    config.yAxis = { 
      type: 'value',
      axisLabel: { color: styles.titleColor },
      axisLine: { lineStyle: { color: styles.titleColor } }
    };
    
    const seriesData = chartData.series || [chartData];
    config.series = seriesData.map((item, index) => ({
      name: item.name || chartData.title || '数据',
      type: 'line',
      data: item.data || 
            (chartData.monthlyTrend ? chartData.monthlyTrend.map(trend => 
              trend.value || trend.amount || trend.count || trend.volume) : []),
      smooth: true,
      lineStyle: {
        color: deviceType === 'wall' ? '#00d4ff' : '#3b82f6'
      },
      itemStyle: {
        color: deviceType === 'wall' ? '#00d4ff' : '#3b82f6'
      },
      areaStyle: chartData.type === 'area' ? { 
        opacity: 0.3,
        color: deviceType === 'wall' ? 'rgba(0, 212, 255, 0.3)' : 'rgba(59, 130, 246, 0.3)'
      } : undefined
    }));
    
    return config;
  },

  // 主要创建图表方法
  createChart(chartData, deviceType = 'pc') {
    if (!chartData) return null;
    
    const chartType = chartData.type || 'line';
    
    switch (chartType) {
      case 'pie':
      case 'doughnut':
        return this.createPieChart(chartData, deviceType);
      case 'bar':
      case 'horizontalBar':
        return this.createBarChart(chartData, deviceType);
      case 'line':
      case 'area':
        return this.createLineChart(chartData, deviceType);
      default:
        return this.createLineChart(chartData, deviceType);
    }
  }
};

if (typeof window !== 'undefined') {
  window.ChartMiddleware = ChartMiddleware;
}

console.log('✅ 完整图表中间件初始化完成');
