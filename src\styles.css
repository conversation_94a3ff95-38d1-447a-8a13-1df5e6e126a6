/* 海南海关领导视窗系统 - 全局样式 */

/* 全局重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  width: 100%;
  height: 100%;
  font-family: 'Microsoft YaHei', 'Helvetica Neue', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: #0c1426;
  overflow: hidden;
}

#app {
  width: 100%;
  height: 100%;
}

/* 加载状态 */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  font-size: 18px;
  color: #64748b;
  background: #0c1426;
}

.error {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  font-size: 18px;
  color: #f56565;
  background: #0c1426;
}

/* ==================== 大屏样式 ==================== */

/* 加工增值大屏 */
.processing-wall {
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #0c1426 0%, #1a2332 50%, #2d3748 100%);
  color: white;
  overflow: hidden;
}

.transport-wall {
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  color: white;
  overflow: hidden;
}

.wall-header {
  position: relative;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #1a5276 0%, #2874a6 100%);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.header-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

.header-content {
  position: relative;
  z-index: 1;
  text-align: center;
}

.header-title {
  font-size: 42px;
  font-weight: bold;
  margin: 0 0 8px 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.header-stats {
  display: flex;
  gap: 40px;
  justify-content: center;
  margin-bottom: 8px;
}

.stat-item {
  font-size: 16px;
  opacity: 0.9;
}

.header-time {
  font-size: 18px;
  opacity: 0.9;
  font-family: 'Courier New', monospace;
}

.wall-main {
  height: calc(100vh - 120px);
  padding: 20px;
}

.charts-grid {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 20px;
  height: 100%;
}

.chart-card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  overflow: hidden;
  transition: all 0.3s ease;
}

.chart-card:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.chart-large {
  grid-row: span 2;
}

.chart-title {
  padding: 20px 24px 0;
  font-size: 18px;
  font-weight: 600;
  color: #fff;
  text-align: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  margin-bottom: 0;
  padding-bottom: 16px;
}

.chart-container {
  height: calc(100% - 60px);
  padding: 20px;
}

/* ==================== PC端样式 ==================== */

.processing-pc {
  width: 100vw;
  height: 100vh;
  background: #f8fafc;
  color: #1e293b;
  overflow: hidden;
}

.pc-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 40px;
  background: white;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  height: 80px;
}

.pc-header h1 {
  font-size: 24px;
  font-weight: bold;
  color: #1e293b;
}

.pc-main {
  padding: 20px;
  height: calc(100vh - 80px);
  overflow-y: auto;
}

.overview-cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin-bottom: 30px;
}

.card {
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  text-align: center;
}

.card-title {
  font-size: 14px;
  color: #64748b;
  margin-bottom: 8px;
}

.card-value {
  font-size: 28px;
  font-weight: bold;
  color: #1a5276;
}

.unit {
  font-size: 16px;
  color: #64748b;
  margin-left: 4px;
}

.charts-container {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
}

.chart-item {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.chart-item .chart-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 16px;
  color: #1e293b;
  text-align: center;
}

.chart-item .chart-container {
  height: 300px;
  width: 100%;
  padding: 0;
}

/* ==================== 移动端样式 ==================== */

.processing-mobile {
  width: 100vw;
  height: 100vh;
  background: #f8fafc;
  color: #1e293b;
  overflow: hidden;
}

.mobile-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: linear-gradient(135deg, #1a5276, #2874a6);
  color: white;
  height: 70px;
}

.mobile-header h1 {
  font-size: 18px;
  font-weight: bold;
}

.mobile-main {
  padding: 16px;
  height: calc(100vh - 70px);
  overflow-y: auto;
}

.mobile-cards {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 20px;
}

.mobile-card {
  background: white;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.mobile-card .card-title {
  font-size: 14px;
  color: #64748b;
}

.mobile-card .card-value {
  font-size: 20px;
  font-weight: bold;
  color: #1a5276;
}

.mobile-chart {
  background: white;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.mobile-chart .chart-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 12px;
  color: #1e293b;
  text-align: center;
}

.mobile-chart .chart-container {
  height: 250px;
  width: 100%;
  padding: 0;
}
