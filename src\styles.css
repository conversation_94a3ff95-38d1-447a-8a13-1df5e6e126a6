/* 海南海关领导视窗系统 - 样式文件 */

/* 全局样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Microsoft YaHei', Arial, sans-serif;
  background: #f8fafc;
  color: #1e293b;
}

/* PC端样式 */
.pc-container {
  background: #f8fafc;
  color: #1e293b;
  min-height: 100vh;
  padding: 20px;
}

.pc-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.pc-title {
  font-size: 24px;
  font-weight: bold;
  color: #1e293b;
}

.pc-time {
  font-size: 16px;
  color: #64748b;
}

.pc-main {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.pc-overview {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
}

.pc-data-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  display: flex;
  align-items: center;
  gap: 15px;
}

.data-card-icon {
  font-size: 32px;
}

.data-card-content {
  flex: 1;
}

.data-card-title {
  font-size: 14px;
  color: #64748b;
  margin-bottom: 5px;
}

.data-card-value {
  font-size: 24px;
  font-weight: bold;
  color: #1e293b;
}

.data-card-unit {
  font-size: 14px;
  color: #64748b;
  margin-left: 5px;
}

.data-card-growth {
  font-size: 12px;
  margin-top: 5px;
}

.data-card-growth.positive {
  color: #10b981;
}

.data-card-growth.negative {
  color: #ef4444;
}

.pc-charts {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
}

.pc-chart-container {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.chart-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 15px;
  color: #1e293b;
}

.chart-content {
  height: 300px;
  width: 100%;
}

/* 移动端样式 */
.mobile-container {
  background: #f8fafc;
  color: #1e293b;
  min-height: 100vh;
  padding: 10px;
  max-width: 100vw;
  overflow-x: hidden;
}

.mobile-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 15px;
  padding: 12px;
  background: linear-gradient(135deg, #3b82f6, #06b6d4);
  border-radius: 8px;
  color: white;
  box-shadow: 0 2px 10px rgba(59, 130, 246, 0.3);
}

.mobile-title {
  font-size: 16px;
  font-weight: bold;
  text-align: center;
  margin-bottom: 4px;
}

.mobile-time {
  font-size: 12px;
  opacity: 0.9;
}

.mobile-main {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.mobile-overview {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.mobile-data-card {
  background: white;
  padding: 12px;
  border-radius: 6px;
  box-shadow: 0 1px 4px rgba(0,0,0,0.1);
  display: flex;
  align-items: center;
  gap: 10px;
  width: 100%;
}

.mobile-data-card .data-card-icon {
  font-size: 18px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #3b82f6, #06b6d4);
  border-radius: 6px;
}

.mobile-data-card .data-card-content {
  flex: 1;
}

.mobile-data-card .data-card-title {
  font-size: 12px;
  color: #64748b;
  margin-bottom: 2px;
}

.mobile-data-card .data-card-value {
  font-size: 16px;
  font-weight: bold;
  color: #1e293b;
}

.mobile-data-card .data-card-unit {
  font-size: 12px;
  color: #64748b;
  margin-left: 2px;
}

.mobile-data-card .data-card-growth {
  font-size: 11px;
  font-weight: 500;
}

.mobile-charts {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.mobile-chart-container {
  background: white;
  padding: 12px;
  border-radius: 6px;
  box-shadow: 0 1px 4px rgba(0,0,0,0.1);
}

.mobile-chart-container .chart-title {
  font-size: 14px;
  font-weight: bold;
  color: #1e293b;
  margin-bottom: 8px;
  text-align: center;
}

.mobile-chart-container .chart-content {
  height: 180px;
}

/* 墙面端样式 */
.wall-container {
  background: linear-gradient(135deg, #0a0e1a 0%, #1a1f2e 100%);
  color: #ffffff;
  min-height: 100vh;
  padding: 0;
  overflow: hidden;
}

.wall-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 40px;
  background: linear-gradient(90deg, #1e3a8a 0%, #3b82f6 100%);
  box-shadow: 0 4px 20px rgba(0,0,0,0.3);
  height: 80px;
}

.wall-title {
  font-size: 42px;
  font-weight: bold;
  color: #ffffff;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.wall-time {
  font-size: 24px;
  color: #e2e8f0;
  font-weight: 500;
}

.wall-main {
  padding: 20px;
  height: calc(100vh - 80px);
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.overview-section {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  height: 180px;
}

.data-card {
  background: linear-gradient(135deg, #1e293b, #334155);
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.3);
  display: flex;
  align-items: center;
  gap: 20px;
  border: 1px solid #475569;
}

.charts-section {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  flex: 1;
  height: calc(100vh - 300px);
}

.chart-container {
  background: linear-gradient(135deg, #1e293b, #334155);
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.3);
  border: 1px solid #475569;
}

.wall-container .chart-title {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 20px;
  color: #00d4ff;
}

.wall-container .chart-content {
  height: 400px;
  background: rgba(0,0,0,0.2);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #94a3b8;
}

.wall-container .data-card-value {
  color: #00d4ff;
}

.wall-container .data-card-title {
  color: #e2e8f0;
}

.wall-container .data-card-unit {
  color: #94a3b8;
}

/* 新增样式 - 与app.js匹配 */
.header-stats {
  display: flex;
  gap: 30px;
}

.stat-item {
  text-align: center;
}

.stat-label {
  display: block;
  font-size: 14px;
  color: #64748b;
  margin-bottom: 5px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #3b82f6;
}

.pc-content {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.chart-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  min-height: 600px;
}

.chart-card {
  background: #ffffff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.chart-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.chart-container {
  width: 100%;
  height: 300px;
}

/* 移动端新增样式 */
.mobile-header h2 {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 5px;
}

.mobile-header p {
  font-size: 14px;
  opacity: 0.9;
  margin-bottom: 10px;
}

.mobile-stats {
  display: flex;
  gap: 15px;
}

.mobile-stat {
  font-size: 14px;
  font-weight: bold;
  background: rgba(255, 255, 255, 0.2);
  padding: 4px 8px;
  border-radius: 4px;
}

.mobile-content {
  flex: 1;
}

.mobile-chart-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.mobile-chart-card {
  background: white;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 大屏新增样式 */
.wall-header h1 {
  font-size: 36px;
  font-weight: bold;
  margin-bottom: 5px;
}

.wall-header h2 {
  font-size: 24px;
  font-weight: 500;
  margin-bottom: 15px;
}

.wall-stats {
  display: flex;
  gap: 40px;
}

.wall-stat-item {
  text-align: center;
}

.wall-stat-value {
  font-size: 32px;
  font-weight: bold;
  color: #00d4ff;
  display: block;
}

.wall-stat-label {
  font-size: 14px;
  color: #e2e8f0;
  margin-top: 5px;
}

.wall-content {
  flex: 1;
  padding: 20px;
}

.wall-chart-grid {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  grid-template-rows: 2fr 1fr;
  gap: 20px;
  height: 100%;
}

.wall-chart-large {
  grid-row: span 2;
  background: linear-gradient(135deg, #1e293b, #334155);
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  border: 1px solid #475569;
}

.wall-chart-medium {
  background: linear-gradient(135deg, #1e293b, #334155);
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  border: 1px solid #475569;
}

.wall-chart-small {
  background: linear-gradient(135deg, #1e293b, #334155);
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  border: 1px solid #475569;
}
