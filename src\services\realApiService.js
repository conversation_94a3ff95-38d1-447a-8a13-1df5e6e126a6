// 海南海关领导视窗系统 - 真实API服务层
console.log('🔧 初始化真实API服务层...');

// ==================== 真实API服务层 ====================
const RealApiService = {
  // 基础配置
  config: {
    baseURL: '/api',
    timeout: 10000,
    retryCount: 3
  },

  // 通用请求方法
  async request(endpoint, options = {}) {
    const url = `${this.config.baseURL}${endpoint}`;
    const config = {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest',
        ...options.headers
      },
      timeout: this.config.timeout,
      ...options
    };

    try {
      console.log(`📡 API请求: ${endpoint}`);
      const response = await fetch(url, config);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      console.log(`✅ API响应: ${endpoint}`, data);
      return data;
    } catch (error) {
      console.error(`❌ API请求失败 [${endpoint}]:`, error);
      
      // 在开发环境下，如果API不可用，使用仿真数据
      if (typeof MockBackendService !== 'undefined') {
        console.log(`🔄 使用仿真数据替代: ${endpoint}`);
        return this.fallbackToMockData(endpoint);
      }
      
      throw error;
    }
  },

  // 仿真数据回退
  async fallbackToMockData(endpoint) {
    const mockMethods = {
      '/processing/overview': () => MockBackendService.getProcessingOverview(),
      '/processing/cargo-trend': () => MockBackendService.getProcessingCargoTrend(),
      '/processing/tax-exemption': () => MockBackendService.getProcessingTaxExemption(),
      '/processing/enterprise-types': () => MockBackendService.getProcessingEnterpriseTypes(),
      '/processing/region-distribution': () => MockBackendService.getProcessingRegionDistribution(),
      '/processing/top-enterprises': () => MockBackendService.getProcessingTopEnterprises(),
      '/processing/main-products': () => MockBackendService.getProcessingMainProducts(),
      
      '/transport/overview': () => MockBackendService.getTransportOverview(),
      '/transport/car-imports': () => MockBackendService.getTransportCarImports(),
      '/transport/duty-free-vehicles': () => MockBackendService.getTransportDutyFreeVehicles(),
      '/transport/vehicle-types': () => MockBackendService.getTransportVehicleTypes(),
      '/transport/clearance-efficiency': () => MockBackendService.getTransportClearanceEfficiency(),
      '/transport/tax-exemption-ranking': () => MockBackendService.getTransportTaxExemptionRanking(),
      '/transport/equipment-ranking': () => MockBackendService.getTransportEquipmentRanking()
    };

    const mockMethod = mockMethods[endpoint];
    if (mockMethod) {
      return await mockMethod();
    }

    throw new Error(`未找到对应的仿真数据方法: ${endpoint}`);
  },

  // 加工增值模块API
  async getProcessingOverview() {
    return await this.request('/processing/overview');
  },

  async getProcessingCargoTrend() {
    return await this.request('/processing/cargo-trend');
  },

  async getProcessingTaxExemption() {
    return await this.request('/processing/tax-exemption');
  },

  async getProcessingEnterpriseTypes() {
    return await this.request('/processing/enterprise-types');
  },

  async getProcessingRegionDistribution() {
    return await this.request('/processing/region-distribution');
  },

  async getProcessingTopEnterprises() {
    return await this.request('/processing/top-enterprises');
  },

  async getProcessingMainProducts() {
    return await this.request('/processing/main-products');
  },

  // 交通工具模块API
  async getTransportOverview() {
    return await this.request('/transport/overview');
  },

  async getTransportCarImports() {
    return await this.request('/transport/car-imports');
  },

  async getTransportDutyFreeVehicles() {
    return await this.request('/transport/duty-free-vehicles');
  },

  async getTransportVehicleTypes() {
    return await this.request('/transport/vehicle-types');
  },

  async getTransportClearanceEfficiency() {
    return await this.request('/transport/clearance-efficiency');
  },

  async getTransportTaxExemptionRanking() {
    return await this.request('/transport/tax-exemption-ranking');
  },

  async getTransportEquipmentRanking() {
    return await this.request('/transport/equipment-ranking');
  },

  // 健康检查
  async healthCheck() {
    try {
      const response = await this.request('/health');
      return response.status === 'ok';
    } catch (error) {
      console.warn('⚠️ API健康检查失败，将使用仿真数据');
      return false;
    }
  }
};

// 导出服务
if (typeof window !== 'undefined') {
  window.RealApiService = RealApiService;
}

console.log('✅ 真实API服务层初始化完成');
