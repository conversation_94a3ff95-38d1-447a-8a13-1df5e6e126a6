// 简化测试版本
console.log('🚀 测试应用启动...');

function initApp() {
  console.log('🔧 初始化应用...');
  
  const app = document.getElementById('app');
  if (!app) {
    console.error('❌ 找不到app元素');
    return;
  }
  
  app.innerHTML = `
    <div style="padding: 20px; background: white; margin: 20px; border-radius: 8px;">
      <h1>海南海关领导视窗系统</h1>
      <p>系统正常运行！</p>
      <div id="chart1" style="width: 100%; height: 400px; background: #f0f0f0; margin: 20px 0;">
        图表区域
      </div>
    </div>
  `;
  
  console.log('✅ 页面渲染完成');
  
  // 测试ECharts
  if (typeof echarts !== 'undefined') {
    console.log('✅ ECharts已加载');
    const chartElement = document.getElementById('chart1');
    if (chartElement) {
      const chart = echarts.init(chartElement);
      const option = {
        title: { text: '测试图表' },
        xAxis: { type: 'category', data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'] },
        yAxis: { type: 'value' },
        series: [{ data: [120, 200, 150, 80, 70, 110, 130], type: 'bar' }]
      };
      chart.setOption(option);
      console.log('✅ 图表创建成功');
    }
  } else {
    console.warn('⚠️ ECharts未加载');
  }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', initApp);

console.log('✅ 测试应用脚本加载完成');
