<template>
  <div class="chart-container" :class="[`chart-container--${deviceType}`, { 'chart-container--loading': loading }]">
    <div class="chart-header" v-if="title">
      <h3 class="chart-title">{{ title }}</h3>
    </div>
    
    <div class="chart-content">
      <div 
        ref="chartRef" 
        class="chart-wrapper"
        :style="chartStyle"
      ></div>
      
      <div v-if="loading" class="chart-loading">
        <div class="loading-spinner"></div>
        <div class="loading-text">图表加载中...</div>
      </div>
      
      <div v-if="error" class="chart-error">
        <div class="error-icon">⚠</div>
        <div class="error-text">{{ error }}</div>
        <button class="retry-button" @click="$emit('retry')">重试</button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ChartContainer',
  props: {
    title: {
      type: String,
      default: ''
    },
    config: {
      type: Object,
      default: () => ({})
    },
    deviceType: {
      type: String,
      default: 'pc',
      validator: value => ['mobile', 'pc', 'wall'].includes(value)
    },
    loading: {
      type: Boolean,
      default: false
    },
    error: {
      type: String,
      default: ''
    },
    height: {
      type: [String, Number],
      default: 'auto'
    }
  },
  emits: ['retry'],
  data() {
    return {
      chart: null,
      resizeObserver: null
    };
  },
  computed: {
    chartStyle() {
      const heights = {
        mobile: '300px',
        pc: '400px',
        wall: '500px'
      };
      
      const height = this.height === 'auto' 
        ? heights[this.deviceType] 
        : (typeof this.height === 'number' ? `${this.height}px` : this.height);
        
      return {
        height,
        width: '100%'
      };
    }
  },
  watch: {
    config: {
      handler(newConfig) {
        if (newConfig && Object.keys(newConfig).length > 0) {
          this.updateChart();
        }
      },
      deep: true
    },
    loading(newVal) {
      if (!newVal && this.config && Object.keys(this.config).length > 0) {
        this.$nextTick(() => {
          this.updateChart();
        });
      }
    }
  },
  mounted() {
    this.initChart();
    this.setupResizeObserver();
  },
  beforeUnmount() {
    this.destroyChart();
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
    }
  },
  methods: {
    initChart() {
      if (!this.$refs.chartRef || !window.echarts) {
        console.warn('ECharts未加载或DOM未准备好');
        return;
      }

      try {
        this.chart = window.echarts.init(this.$refs.chartRef);
        
        if (this.config && Object.keys(this.config).length > 0) {
          this.updateChart();
        }
      } catch (error) {
        console.error('初始化图表失败:', error);
        this.$emit('retry');
      }
    },
    
    updateChart() {
      if (!this.chart || this.loading) return;
      
      try {
        this.chart.setOption(this.config, true);
      } catch (error) {
        console.error('更新图表失败:', error);
        this.$emit('retry');
      }
    },
    
    destroyChart() {
      if (this.chart) {
        this.chart.dispose();
        this.chart = null;
      }
    },
    
    setupResizeObserver() {
      if (!window.ResizeObserver) return;
      
      this.resizeObserver = new ResizeObserver(() => {
        if (this.chart) {
          this.chart.resize();
        }
      });
      
      if (this.$refs.chartRef) {
        this.resizeObserver.observe(this.$refs.chartRef);
      }
    }
  }
};
</script>

<style scoped>
.chart-container {
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
}

.chart-container:hover {
  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
}

.chart-container--loading {
  opacity: 0.7;
}

.chart-header {
  padding: 16px 20px 0;
  border-bottom: 1px solid #f0f0f0;
}

.chart-title {
  font-size: 16px;
  color: #1a5276;
  margin: 0 0 16px 0;
  font-weight: 600;
  text-align: center;
}

.chart-content {
  position: relative;
  padding: 20px;
}

.chart-wrapper {
  width: 100%;
}

.chart-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: rgba(255, 255, 255, 0.9);
  gap: 12px;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #1a5276;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: 14px;
  color: #666;
}

.chart-error {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: rgba(255, 255, 255, 0.95);
  gap: 12px;
}

.error-icon {
  font-size: 32px;
  color: #f5222d;
}

.error-text {
  font-size: 14px;
  color: #666;
  text-align: center;
}

.retry-button {
  padding: 8px 16px;
  background: #1a5276;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.3s ease;
}

.retry-button:hover {
  background: #2874a6;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 移动端样式 */
.chart-container--mobile {
  border-radius: 6px;
}

.chart-container--mobile .chart-header {
  padding: 12px 16px 0;
}

.chart-container--mobile .chart-title {
  font-size: 14px;
  margin-bottom: 12px;
}

.chart-container--mobile .chart-content {
  padding: 16px;
}

/* 大屏样式 */
.chart-container--wall {
  border-radius: 12px;
}

.chart-container--wall .chart-header {
  padding: 24px 30px 0;
}

.chart-container--wall .chart-title {
  font-size: 20px;
  margin-bottom: 20px;
}

.chart-container--wall .chart-content {
  padding: 30px;
}

.chart-container--wall .loading-spinner {
  width: 40px;
  height: 40px;
  border-width: 4px;
}

.chart-container--wall .loading-text {
  font-size: 16px;
}

.chart-container--wall .error-icon {
  font-size: 40px;
}

.chart-container--wall .error-text {
  font-size: 16px;
}
</style>
