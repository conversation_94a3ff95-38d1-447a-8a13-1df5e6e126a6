/**
* @vue/reactivity v3.5.0
* (c) 2018-present <PERSON><PERSON> (<PERSON>) <PERSON> and Vue contributors
* @license MIT
**/var VueReactivity=function(e){"use strict";let t,i,s,r;let n={},l=()=>{},a=Object.assign,o=(e,t)=>{let i=e.indexOf(t);i>-1&&e.splice(i,1)},u=Object.prototype.hasOwnProperty,f=(e,t)=>u.call(e,t),c=Array.isArray,h=e=>"[object Map]"===R(e),p=e=>"[object Set]"===R(e),d=e=>"function"==typeof e,v=e=>"string"==typeof e,_=e=>"symbol"==typeof e,g=e=>null!==e&&"object"==typeof e,y=Object.prototype.toString,R=e=>y.call(e),b=e=>R(e).slice(8,-1),w=e=>"[object Object]"===R(e),S=e=>v(e)&&"NaN"!==e&&"-"!==e[0]&&""+parseInt(e,10)===e,E=(e,t)=>!Object.is(e,t),x=(e,t,i,s=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:s,value:i})};class T{constructor(e=!1){this.detached=e,this._active=!0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=t,!e&&t&&(this.index=(t.scopes||(t.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){let e,t;if(this._isPaused=!0,this.scopes)for(e=0,t=this.scopes.length;e<t;e++)this.scopes[e].pause();for(e=0,t=this.effects.length;e<t;e++)this.effects[e].pause()}}resume(){if(this._active&&this._isPaused){let e,t;if(this._isPaused=!1,this.scopes)for(e=0,t=this.scopes.length;e<t;e++)this.scopes[e].resume();for(e=0,t=this.effects.length;e<t;e++)this.effects[e].resume()}}run(e){if(this._active){let i=t;try{return t=this,e()}finally{t=i}}}on(){t=this}off(){t=this.parent}stop(e){if(this._active){let t,i;for(t=0,i=this.effects.length;t<i;t++)this.effects[t].stop();for(t=0,i=this.cleanups.length;t<i;t++)this.cleanups[t]();if(this.scopes)for(t=0,i=this.scopes.length;t<i;t++)this.scopes[t].stop(!0);if(!this.detached&&this.parent&&!e){let e=this.parent.scopes.pop();e&&e!==this&&(this.parent.scopes[this.index]=e,e.index=this.index)}this.parent=void 0,this._active=!1}}}let A=/* @__PURE__ */new WeakSet;class m{constructor(e){this.fn=e,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.nextEffect=void 0,this.cleanup=void 0,this.scheduler=void 0,t&&t.active&&t.effects.push(this)}pause(){this.flags|=64}resume(){64&this.flags&&(this.flags&=-65,A.has(this)&&(A.delete(this),this.trigger()))}notify(){(!(2&this.flags)||32&this.flags)&&(8&this.flags||(this.flags|=8,this.nextEffect=s,s=this))}run(){if(!(1&this.flags))return this.fn();this.flags|=2,K(this),O(this);let e=i,t=j;i=this,j=!0;try{return this.fn()}finally{I(this),i=e,j=t,this.flags&=-3}}stop(){if(1&this.flags){for(let e=this.deps;e;e=e.nextDep)P(e);this.deps=this.depsTail=void 0,K(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){64&this.flags?A.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){L(this)&&this.run()}get dirty(){return L(this)}}let k=0;function D(){let e;if(!(--k>0)){for(;s;){let t=s;for(s=void 0;t;){let i=t.nextEffect;if(t.nextEffect=void 0,t.flags&=-9,1&t.flags)try{t.trigger()}catch(t){e||(e=t)}t=i}}if(e)throw e}}function O(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function I(e){let t;let i=e.depsTail;for(let e=i;e;e=e.prevDep)-1===e.version?(e===i&&(i=e.prevDep),P(e),function(e){let{prevDep:t,nextDep:i}=e;t&&(t.nextDep=i,e.prevDep=void 0),i&&(i.prevDep=t,e.nextDep=void 0)}(e)):t=e,e.dep.activeLink=e.prevActiveLink,e.prevActiveLink=void 0;e.deps=t,e.depsTail=i}function L(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&!1===C(t.dep.computed)||t.dep.version!==t.version)return!0;return!!e._dirty}function C(e){if(2&e.flags)return!1;if(4&e.flags&&!(16&e.flags)||(e.flags&=-17,e.globalVersion===M))return;e.globalVersion=M;let t=e.dep;if(e.flags|=2,t.version>0&&!e.isSSR&&!L(e)){e.flags&=-3;return}let s=i,r=j;i=e,j=!0;try{O(e);let i=e.fn();(0===t.version||E(i,e._value))&&(e._value=i,t.version++)}catch(e){throw t.version++,e}finally{i=s,j=r,I(e),e.flags&=-3}}function P(e){let{dep:t,prevSub:i,nextSub:s}=e;if(i&&(i.nextSub=s,e.prevSub=void 0),s&&(s.prevSub=i,e.nextSub=void 0),t.subs===e&&(t.subs=i),!t.subs&&t.computed){t.computed.flags&=-5;for(let e=t.computed.deps;e;e=e.nextDep)P(e)}}let j=!0,W=[];function N(){W.push(j),j=!1}function V(){let e=W.pop();j=void 0===e||e}function K(e){let{cleanup:t}=e;if(e.cleanup=void 0,t){let e=i;i=void 0;try{t()}finally{i=e}}}let M=0;class H{constructor(e){this.computed=e,this.version=0,this.activeLink=void 0,this.subs=void 0}track(e){if(!i||!j)return;let t=this.activeLink;if(void 0===t||t.sub!==i)t=this.activeLink={dep:this,sub:i,version:this.version,nextDep:void 0,prevDep:void 0,nextSub:void 0,prevSub:void 0,prevActiveLink:void 0},i.deps?(t.prevDep=i.depsTail,i.depsTail.nextDep=t,i.depsTail=t):i.deps=i.depsTail=t,4&i.flags&&function e(t){let i=t.dep.computed;if(i&&!t.dep.subs){i.flags|=20;for(let t=i.deps;t;t=t.nextDep)e(t)}let s=t.dep.subs;s!==t&&(t.prevSub=s,s&&(s.nextSub=t)),t.dep.subs=t}(t);else if(-1===t.version&&(t.version=this.version,t.nextDep)){let e=t.nextDep;e.prevDep=t.prevDep,t.prevDep&&(t.prevDep.nextDep=e),t.prevDep=i.depsTail,t.nextDep=void 0,i.depsTail.nextDep=t,i.depsTail=t,i.deps===t&&(i.deps=e)}return t}trigger(e){this.version++,M++,this.notify(e)}notify(e){k++;try{for(let e=this.subs;e;e=e.prevSub)e.sub.notify()}finally{D()}}}let U=/* @__PURE__ */new WeakMap,Y=Symbol(""),G=Symbol(""),z=Symbol("");function F(e,t,s){if(j&&i){let t=U.get(e);t||U.set(e,t=/* @__PURE__ */new Map);let i=t.get(s);i||t.set(s,i=new H),i.track()}}function B(e,t,i,s,r,n){let l=U.get(e);if(!l){M++;return}let a=[];if("clear"===t)a=[...l.values()];else{let r=c(e),n=r&&S(i);if(r&&"length"===i){let e=Number(s);l.forEach((t,i)=>{("length"===i||i===z||!_(i)&&i>=e)&&a.push(t)})}else{let s=e=>e&&a.push(e);switch(void 0!==i&&s(l.get(i)),n&&s(l.get(z)),t){case"add":r?n&&s(l.get("length")):(s(l.get(Y)),h(e)&&s(l.get(G)));break;case"delete":!r&&(s(l.get(Y)),h(e)&&s(l.get(G)));break;case"set":h(e)&&s(l.get(Y))}}}for(let e of(k++,a))e.trigger();D()}function q(e){let t=ez(e);return t===e?t:(F(t,"iterate",z),eY(e)?t:t.map(eF))}function J(e){return F(e=ez(e),"iterate",z),e}let Q={__proto__:null,[Symbol.iterator](){return X(this,Symbol.iterator,eF)},concat(...e){return q(this).concat(...e.map(e=>q(e)))},entries(){return X(this,"entries",e=>(e[1]=eF(e[1]),e))},every(e,t){return $(this,"every",e,t,void 0,arguments)},filter(e,t){return $(this,"filter",e,t,e=>e.map(eF),arguments)},find(e,t){return $(this,"find",e,t,eF,arguments)},findIndex(e,t){return $(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return $(this,"findLast",e,t,eF,arguments)},findLastIndex(e,t){return $(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return $(this,"forEach",e,t,void 0,arguments)},includes(...e){return et(this,"includes",e)},indexOf(...e){return et(this,"indexOf",e)},join(e){return q(this).join(e)},lastIndexOf(...e){return et(this,"lastIndexOf",e)},map(e,t){return $(this,"map",e,t,void 0,arguments)},pop(){return ei(this,"pop")},push(...e){return ei(this,"push",e)},reduce(e,...t){return ee(this,"reduce",e,t)},reduceRight(e,...t){return ee(this,"reduceRight",e,t)},shift(){return ei(this,"shift")},some(e,t){return $(this,"some",e,t,void 0,arguments)},splice(...e){return ei(this,"splice",e)},toReversed(){return q(this).toReversed()},toSorted(e){return q(this).toSorted(e)},toSpliced(...e){return q(this).toSpliced(...e)},unshift(...e){return ei(this,"unshift",e)},values(){return X(this,"values",eF)}};function X(e,t,i){let s=J(e),r=s[t]();return s===e||eY(e)||(r._next=r.next,r.next=()=>{let e=r._next();return e.value&&(e.value=i(e.value)),e}),r}let Z=Array.prototype;function $(e,t,i,s,r,n){let l=J(e),a=l!==e&&!eY(e),o=l[t];if(o!==Z[t]){let t=o.apply(e,n);return a?eF(t):t}let u=i;l!==e&&(a?u=function(t,s){return i.call(this,eF(t),s,e)}:i.length>2&&(u=function(t,s){return i.call(this,t,s,e)}));let f=o.call(l,u,s);return a&&r?r(f):f}function ee(e,t,i,s){let r=J(e),n=i;return r!==e&&(eY(e)?i.length>3&&(n=function(t,s,r){return i.call(this,t,s,r,e)}):n=function(t,s,r){return i.call(this,t,eF(s),r,e)}),r[t](n,...s)}function et(e,t,i){let s=ez(e);F(s,"iterate",z);let r=s[t](...i);return(-1===r||!1===r)&&eG(i[0])?(i[0]=ez(i[0]),s[t](...i)):r}function ei(e,t,i=[]){N(),k++;let s=ez(e)[t].apply(e,i);return D(),V(),s}let es=/* @__PURE__ *//*! #__NO_SIDE_EFFECTS__ */function(e,t){let i=new Set(e.split(","));return e=>i.has(e)}("__proto__,__v_isRef,__isVue"),er=new Set(/* @__PURE__ */Object.getOwnPropertyNames(Symbol).filter(e=>"arguments"!==e&&"caller"!==e).map(e=>Symbol[e]).filter(_));function en(e){_(e)||(e=String(e));let t=ez(this);return F(t,"has",e),t.hasOwnProperty(e)}class el{constructor(e=!1,t=!1){this._isReadonly=e,this._isShallow=t}get(e,t,i){let s=this._isReadonly,r=this._isShallow;if("__v_isReactive"===t)return!s;if("__v_isReadonly"===t)return s;if("__v_isShallow"===t)return r;if("__v_raw"===t)return i===(s?r?eN:eW:r?ej:eP).get(e)||Object.getPrototypeOf(e)===Object.getPrototypeOf(i)?e:void 0;let n=c(e);if(!s){let e;if(n&&(e=Q[t]))return e;if("hasOwnProperty"===t)return en}let l=Reflect.get(e,t,eq(e)?e:i);return(_(t)?er.has(t):es(t))?l:(s||F(e,"get",t),r)?l:eq(l)?n&&S(t)?l:l.value:g(l)?s?eK(l):eV(l):l}}class ea extends el{constructor(e=!1){super(!1,e)}set(e,t,i,s){let r=e[t];if(!this._isShallow){let t=eU(r);if(eY(i)||eU(i)||(r=ez(r),i=ez(i)),!c(e)&&eq(r)&&!eq(i))return!t&&(r.value=i,!0)}let n=c(e)&&S(t)?Number(t)<e.length:f(e,t),l=Reflect.set(e,t,i,eq(e)?e:s);return e===ez(s)&&(n?E(i,r)&&B(e,"set",t,i):B(e,"add",t,i)),l}deleteProperty(e,t){let i=f(e,t);e[t];let s=Reflect.deleteProperty(e,t);return s&&i&&B(e,"delete",t,void 0),s}has(e,t){let i=Reflect.has(e,t);return _(t)&&er.has(t)||F(e,"has",t),i}ownKeys(e){return F(e,"iterate",c(e)?"length":Y),Reflect.ownKeys(e)}}class eo extends el{constructor(e=!1){super(!0,e)}set(e,t){return!0}deleteProperty(e,t){return!0}}let eu=/* @__PURE__ */new ea,ef=/* @__PURE__ */new eo,ec=/* @__PURE__ */new ea(!0),eh=/* @__PURE__ */new eo(!0),ep=e=>e,ed=e=>Reflect.getPrototypeOf(e);function ev(e,t,i=!1,s=!1){let r=ez(e=e.__v_raw),n=ez(t);i||(E(t,n)&&F(r,"get",t),F(r,"get",n));let{has:l}=ed(r),a=s?ep:i?eB:eF;return l.call(r,t)?a(e.get(t)):l.call(r,n)?a(e.get(n)):void(e!==r&&e.get(t))}function e_(e,t=!1){let i=this.__v_raw,s=ez(i),r=ez(e);return t||(E(e,r)&&F(s,"has",e),F(s,"has",r)),e===r?i.has(e):i.has(e)||i.has(r)}function eg(e,t=!1){return e=e.__v_raw,t||F(ez(e),"iterate",Y),Reflect.get(e,"size",e)}function ey(e,t=!1){t||eY(e)||eU(e)||(e=ez(e));let i=ez(this);return ed(i).has.call(i,e)||(i.add(e),B(i,"add",e,e)),this}function eR(e,t,i=!1){i||eY(t)||eU(t)||(t=ez(t));let s=ez(this),{has:r,get:n}=ed(s),l=r.call(s,e);l||(e=ez(e),l=r.call(s,e));let a=n.call(s,e);return s.set(e,t),l?E(t,a)&&B(s,"set",e,t):B(s,"add",e,t),this}function eb(e){let t=ez(this),{has:i,get:s}=ed(t),r=i.call(t,e);r||(e=ez(e),r=i.call(t,e)),s&&s.call(t,e);let n=t.delete(e);return r&&B(t,"delete",e,void 0),n}function ew(){let e=ez(this),t=0!==e.size,i=e.clear();return t&&B(e,"clear",void 0,void 0),i}function eS(e,t){return function(i,s){let r=this,n=r.__v_raw,l=ez(n),a=t?ep:e?eB:eF;return e||F(l,"iterate",Y),n.forEach((e,t)=>i.call(s,a(e),a(t),r))}}function eE(e,t,i){return function(...s){let r=this.__v_raw,n=ez(r),l=h(n),a="entries"===e||e===Symbol.iterator&&l,o=r[e](...s),u=i?ep:t?eB:eF;return t||F(n,"iterate","keys"===e&&l?G:Y),{next(){let{value:e,done:t}=o.next();return t?{value:e,done:t}:{value:a?[u(e[0]),u(e[1])]:u(e),done:t}},[Symbol.iterator](){return this}}}}function ex(e){return function(...t){return"delete"!==e&&("clear"===e?void 0:this)}}let[eT,eA,em,ek]=/* @__PURE__ */function(){let e={get(e){return ev(this,e)},get size(){return eg(this)},has:e_,add:ey,set:eR,delete:eb,clear:ew,forEach:eS(!1,!1)},t={get(e){return ev(this,e,!1,!0)},get size(){return eg(this)},has:e_,add(e){return ey.call(this,e,!0)},set(e,t){return eR.call(this,e,t,!0)},delete:eb,clear:ew,forEach:eS(!1,!0)},i={get(e){return ev(this,e,!0)},get size(){return eg(this,!0)},has(e){return e_.call(this,e,!0)},add:ex("add"),set:ex("set"),delete:ex("delete"),clear:ex("clear"),forEach:eS(!0,!1)},s={get(e){return ev(this,e,!0,!0)},get size(){return eg(this,!0)},has(e){return e_.call(this,e,!0)},add:ex("add"),set:ex("set"),delete:ex("delete"),clear:ex("clear"),forEach:eS(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach(r=>{e[r]=eE(r,!1,!1),i[r]=eE(r,!0,!1),t[r]=eE(r,!1,!0),s[r]=eE(r,!0,!0)}),[e,i,t,s]}();function eD(e,t){let i=t?e?ek:em:e?eA:eT;return(t,s,r)=>"__v_isReactive"===s?!e:"__v_isReadonly"===s?e:"__v_raw"===s?t:Reflect.get(f(i,s)&&s in t?i:t,s,r)}let eO={get:/* @__PURE__ */eD(!1,!1)},eI={get:/* @__PURE__ */eD(!1,!0)},eL={get:/* @__PURE__ */eD(!0,!1)},eC={get:/* @__PURE__ */eD(!0,!0)},eP=/* @__PURE__ */new WeakMap,ej=/* @__PURE__ */new WeakMap,eW=/* @__PURE__ */new WeakMap,eN=/* @__PURE__ */new WeakMap;function eV(e){return eU(e)?e:eM(e,!1,eu,eO,eP)}function eK(e){return eM(e,!0,ef,eL,eW)}function eM(e,t,i,s,r){if(!g(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;let n=r.get(e);if(n)return n;let l=e.__v_skip||!Object.isExtensible(e)?0:function(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}(b(e));if(0===l)return e;let a=new Proxy(e,2===l?s:i);return r.set(e,a),a}function eH(e){return eU(e)?eH(e.__v_raw):!!(e&&e.__v_isReactive)}function eU(e){return!!(e&&e.__v_isReadonly)}function eY(e){return!!(e&&e.__v_isShallow)}function eG(e){return!!e&&!!e.__v_raw}function ez(e){let t=e&&e.__v_raw;return t?ez(t):e}let eF=e=>g(e)?eV(e):e,eB=e=>g(e)?eK(e):e;function eq(e){return!!e&&!0===e.__v_isRef}function eJ(e){return eQ(e,!1)}function eQ(e,t){return eq(e)?e:new eX(e,t)}class eX{constructor(e,t){this.dep=new H,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=t?e:ez(e),this._value=t?e:eF(e),this.__v_isShallow=t}get value(){return this.dep.track(),this._value}set value(e){let t=this._rawValue,i=this.__v_isShallow||eY(e)||eU(e);E(e=i?e:ez(e),t)&&(this._rawValue=e,this._value=i?e:eF(e),this.dep.trigger())}}function eZ(e){return eq(e)?e.value:e}let e$={get:(e,t,i)=>eZ(Reflect.get(e,t,i)),set:(e,t,i,s)=>{let r=e[t];return eq(r)&&!eq(i)?(r.value=i,!0):Reflect.set(e,t,i,s)}};class e0{constructor(e){this.__v_isRef=!0,this._value=void 0;let t=this.dep=new H,{get:i,set:s}=e(t.track.bind(t),t.trigger.bind(t));this._get=i,this._set=s}get value(){return this._value=this._get()}set value(e){this._set(e)}}class e1{constructor(e,t,i){this._object=e,this._key=t,this._defaultValue=i,this.__v_isRef=!0,this._value=void 0}get value(){let e=this._object[this._key];return this._value=void 0===e?this._defaultValue:e}set value(e){this._object[this._key]=e}get dep(){var e,t,i;return e=ez(this._object),t=this._key,null==(i=U.get(e))?void 0:i.get(t)}}class e2{constructor(e){this._getter=e,this.__v_isRef=!0,this.__v_isReadonly=!0,this._value=void 0}get value(){return this._value=this._getter()}}function e4(e,t,i){let s=e[t];return eq(s)?s:new e1(e,t,i)}class e3{constructor(e,t,i){this.fn=e,this.setter=t,this._value=void 0,this.dep=new H(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=M-1,this.effect=this,this.__v_isReadonly=!t,this.isSSR=i}notify(){i!==this&&(this.flags|=16,this.dep.notify())}get value(){let e=this.dep.track();return C(this),e&&(e.version=this.dep.version),this._value}set value(e){this.setter&&this.setter(e)}}let e6={},e8=/* @__PURE__ */new WeakMap;function e5(e,t=!1,i=r){if(i){let t=e8.get(i);t||e8.set(i,t=[]),t.push(e)}}function e7(e,t=1/0,i){if(t<=0||!g(e)||e.__v_skip||(i=i||/* @__PURE__ */new Set).has(e))return e;if(i.add(e),t--,eq(e))e7(e.value,t,i);else if(c(e))for(let s=0;s<e.length;s++)e7(e[s],t,i);else if(p(e)||h(e))e.forEach(e=>{e7(e,t,i)});else if(w(e)){for(let s in e)e7(e[s],t,i);for(let s of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,s)&&e7(e[s],t,i)}return e}return e.ARRAY_ITERATE_KEY=z,e.EffectFlags={ACTIVE:1,1:"ACTIVE",RUNNING:2,2:"RUNNING",TRACKING:4,4:"TRACKING",NOTIFIED:8,8:"NOTIFIED",DIRTY:16,16:"DIRTY",ALLOW_RECURSE:32,32:"ALLOW_RECURSE",PAUSED:64,64:"PAUSED"},e.EffectScope=T,e.ITERATE_KEY=Y,e.MAP_KEY_ITERATE_KEY=G,e.ReactiveEffect=m,e.ReactiveFlags={SKIP:"__v_skip",IS_REACTIVE:"__v_isReactive",IS_READONLY:"__v_isReadonly",IS_SHALLOW:"__v_isShallow",RAW:"__v_raw",IS_REF:"__v_isRef"},e.TrackOpTypes={GET:"get",HAS:"has",ITERATE:"iterate"},e.TriggerOpTypes={SET:"set",ADD:"add",DELETE:"delete",CLEAR:"clear"},e.WatchErrorCodes={WATCH_GETTER:2,2:"WATCH_GETTER",WATCH_CALLBACK:3,3:"WATCH_CALLBACK",WATCH_CLEANUP:4,4:"WATCH_CLEANUP"},e.computed=function(e,t,i=!1){let s,r;return d(e)?s=e:(s=e.get,r=e.set),new e3(s,r,i)},e.customRef=function(e){return new e0(e)},e.effect=function(e,t){e.effect instanceof m&&(e=e.effect.fn);let i=new m(e);t&&a(i,t);try{i.run()}catch(e){throw i.stop(),e}let s=i.run.bind(i);return s.effect=i,s},e.effectScope=function(e){return new T(e)},e.enableTracking=function(){W.push(j),j=!0},e.getCurrentScope=function(){return t},e.getCurrentWatcher=function(){return r},e.isProxy=eG,e.isReactive=eH,e.isReadonly=eU,e.isRef=eq,e.isShallow=eY,e.markRaw=function(e){return Object.isExtensible(e)&&x(e,"__v_skip",!0),e},e.onEffectCleanup=function(e,t=!1){i instanceof m&&(i.cleanup=e)},e.onScopeDispose=function(e,i=!1){t&&t.cleanups.push(e)},e.onWatcherCleanup=e5,e.pauseTracking=N,e.proxyRefs=function(e){return eH(e)?e:new Proxy(e,e$)},e.reactive=eV,e.reactiveReadArray=q,e.readonly=eK,e.ref=eJ,e.resetTracking=V,e.shallowReactive=function(e){return eM(e,!1,ec,eI,ej)},e.shallowReadArray=J,e.shallowReadonly=function(e){return eM(e,!0,eh,eC,eN)},e.shallowRef=function(e){return eQ(e,!0)},e.stop=function(e){e.effect.stop()},e.toRaw=ez,e.toReactive=eF,e.toReadonly=eB,e.toRef=function(e,t,i){return eq(e)?e:d(e)?new e2(e):g(e)&&arguments.length>1?e4(e,t,i):eJ(e)},e.toRefs=function(e){let t=c(e)?Array(e.length):{};for(let i in e)t[i]=e4(e,i);return t},e.toValue=function(e){return d(e)?e():eZ(e)},e.track=F,e.traverse=e7,e.trigger=B,e.triggerRef=function(e){e.dep.trigger()},e.unref=eZ,e.watch=function(e,i,s=n){let a,u,f,h;let{immediate:p,deep:v,once:_,scheduler:g,augmentJob:y,call:R}=s,b=e=>v?e:eY(e)||!1===v||0===v?e7(e,1):e7(e),w=!1,S=!1;if(eq(e)?(u=()=>e.value,w=eY(e)):eH(e)?(u=()=>b(e),w=!0):c(e)?(S=!0,w=e.some(e=>eH(e)||eY(e)),u=()=>e.map(e=>eq(e)?e.value:eH(e)?b(e):d(e)?R?R(e,2):e():void 0)):u=d(e)?i?R?()=>R(e,2):e:()=>{if(f){N();try{f()}finally{V()}}let t=r;r=a;try{return R?R(e,3,[h]):e(h)}finally{r=t}}:l,i&&v){let e=u,t=!0===v?1/0:v;u=()=>e7(e(),t)}let x=t,T=()=>{a.stop(),x&&o(x.effects,a)};if(_){if(i){let e=i;i=(...t)=>{e(...t),T()}}else{let e=u;u=()=>{e(),T()}}}let A=S?Array(e.length).fill(e6):e6,k=e=>{if(1&a.flags&&(a.dirty||e)){if(i){let e=a.run();if(v||w||(S?e.some((e,t)=>E(e,A[t])):E(e,A))){f&&f();let t=r;r=a;try{let t=[e,A===e6?void 0:S&&A[0]===e6?[]:A,h];R?R(i,3,t):i(...t),A=e}finally{r=t}}}else a.run()}};return y&&y(k),(a=new m(u)).scheduler=g?()=>g(k,!1):k,h=e=>e5(e,!1,a),f=a.onStop=()=>{let e=e8.get(a);if(e){if(R)R(e,4);else for(let t of e)t();e8.delete(a)}},i?p?k(!0):A=a.run():g?g(k.bind(null,!0),!0):a.run(),T.pause=a.pause.bind(a),T.resume=a.resume.bind(a),T.stop=T,T},e}({});
