const http = require('http');
const fs = require('fs');
const path = require('path');

const PORT = 5173;

console.log('🚀 启动海南海关领导视窗系统...');
console.log('📁 工作目录:', __dirname);

const mimeTypes = {
  '.html': 'text/html; charset=utf-8',
  '.js': 'text/javascript; charset=utf-8',
  '.css': 'text/css; charset=utf-8',
  '.json': 'application/json; charset=utf-8'
};

function getMimeType(filePath) {
  const ext = path.extname(filePath).toLowerCase();
  return mimeTypes[ext] || 'text/plain; charset=utf-8';
}

const server = http.createServer((req, res) => {
  const timestamp = new Date().toLocaleTimeString();
  console.log(`${timestamp} ${req.method} ${req.url}`);

  // 设置CORS头
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  try {
    // 静态文件服务
    if (req.url.startsWith('/src/')) {
      const filePath = path.join(__dirname, req.url);
      console.log(`📁 访问文件: ${filePath}`);

      if (fs.existsSync(filePath) && fs.statSync(filePath).isFile()) {
        const data = fs.readFileSync(filePath, 'utf8');
        const mimeType = getMimeType(filePath);
        res.writeHead(200, {
          'Content-Type': mimeType,
          'Cache-Control': 'no-cache'
        });
        res.end(data);
        console.log(`✅ 文件服务: ${req.url}`);
        return;
      }
    }

    // SPA路由处理 - 所有其他路由都返回index.html
    const data = fs.readFileSync(path.join(__dirname, 'index.html'), 'utf8');
    res.writeHead(200, { 'Content-Type': 'text/html' });
    res.end(data);
  } catch (error) {
    console.error('Server error:', error);
    res.writeHead(500, { 'Content-Type': 'text/plain' });
    res.end('Internal server error: ' + error.message);
  }
});

// 启动服务器
server.listen(PORT, () => {
  console.log(`🚀 开发服务器启动成功: http://localhost:${PORT}`);
  console.log(`📱 移动端访问: http://localhost:${PORT}/processing/mobile`);
  console.log(`💻 PC端访问: http://localhost:${PORT}/processing/pc`);
  console.log(`🖥️  加工增值大屏: http://localhost:${PORT}/processing/wall`);
  console.log(`🚛 交通工具大屏: http://localhost:${PORT}/transport/wall`);
});

server.on('error', (error) => {
  console.error('❌ 服务器启动失败:', error);
});
