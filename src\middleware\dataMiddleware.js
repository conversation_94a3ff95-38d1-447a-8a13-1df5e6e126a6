// 海南海关领导视窗系统 - 数据中间件
console.log('🔧 初始化数据中间件...');

// ==================== 数据中间件层 ====================
const DataMiddleware = {
  // 获取加工增值模块数据
  async getProcessingData() {
    try {
      console.log('📊 获取加工增值数据...');
      
      // 获取概览数据
      const overviewResponse = await MockBackendService.getProcessingOverview();
      
      // 获取图表数据
      const cargoTrendResponse = await MockBackendService.getProcessingCargoTrend();
      const taxExemptionResponse = await MockBackendService.getProcessingTaxExemption();
      const enterpriseTypesResponse = await MockBackendService.getProcessingEnterpriseTypes();
      const regionDistributionResponse = await MockBackendService.getProcessingRegionDistribution();
      const topEnterprisesResponse = await MockBackendService.getProcessingTopEnterprises();
      const mainProductsResponse = await MockBackendService.getProcessingMainProducts();
      
      return {
        overview: overviewResponse.data,
        charts: {
          cargoTrend: cargoTrendResponse.data,
          taxExemption: taxExemptionResponse.data,
          enterpriseTypes: enterpriseTypesResponse.data,
          regionDistribution: regionDistributionResponse.data,
          topEnterprises: topEnterprisesResponse.data,
          mainProducts: mainProductsResponse.data
        }
      };
    } catch (error) {
      console.error('❌ 获取加工增值数据失败:', error);
      throw error;
    }
  },

  // 获取交通工具模块数据
  async getTransportData() {
    try {
      console.log('📊 获取交通工具数据...');
      
      // 获取概览数据
      const overviewResponse = await MockBackendService.getTransportOverview();
      
      // 获取图表数据
      const carImportsResponse = await MockBackendService.getTransportCarImports();
      const dutyFreeVehiclesResponse = await MockBackendService.getTransportDutyFreeVehicles();
      const vehicleTypesResponse = await MockBackendService.getTransportVehicleTypes();
      const clearanceEfficiencyResponse = await MockBackendService.getTransportClearanceEfficiency();
      const taxExemptionRankingResponse = await MockBackendService.getTransportTaxExemptionRanking();
      const equipmentRankingResponse = await MockBackendService.getTransportEquipmentRanking();
      
      return {
        overview: overviewResponse.data,
        charts: {
          carImports: carImportsResponse.data,
          dutyFreeVehicles: dutyFreeVehiclesResponse.data,
          vehicleTypes: vehicleTypesResponse.data,
          clearanceEfficiency: clearanceEfficiencyResponse.data,
          taxExemptionRanking: taxExemptionRankingResponse.data,
          equipmentRanking: equipmentRankingResponse.data
        }
      };
    } catch (error) {
      console.error('❌ 获取交通工具数据失败:', error);
      throw error;
    }
  },

  // 格式化数值显示
  formatValue(value, unit = '') {
    if (typeof value === 'string') return value + unit;
    if (typeof value !== 'number') return value;
    
    if (value >= *********) {
      return (value / *********).toFixed(1) + '亿' + unit;
    } else if (value >= 10000) {
      return (value / 10000).toFixed(1) + '万' + unit;
    }
    return value.toFixed(0) + unit;
  },

  // 格式化增长率显示
  formatGrowth(growth) {
    const num = parseFloat(growth);
    if (isNaN(num)) return growth;
    return (num >= 0 ? '+' : '') + num.toFixed(1) + '%';
  },

  // 处理概览卡片数据
  processOverviewCards(data) {
    return data.map(item => ({
      ...item,
      formattedValue: this.formatValue(item.value, item.unit),
      formattedGrowth: this.formatGrowth(item.growth),
      growthClass: parseFloat(item.growth) >= 0 ? 'positive' : 'negative'
    }));
  },

  // 处理图表数据
  processChartData(chartData, deviceType) {
    if (!chartData) return null;
    
    // 使用图表中间件创建图表配置
    return ChartMiddleware.createChart(chartData, deviceType);
  },

  // 获取完整的模块数据（包含处理后的数据）
  async getModuleData(module, deviceType = 'pc') {
    try {
      let rawData;
      
      if (module === 'processing') {
        rawData = await this.getProcessingData();
      } else if (module === 'transport') {
        rawData = await this.getTransportData();
      } else {
        throw new Error(`未知的模块: ${module}`);
      }

      // 处理概览数据
      const processedOverview = this.processOverviewCards(rawData.overview);

      // 处理图表数据
      const processedCharts = {};
      for (const [key, chartData] of Object.entries(rawData.charts)) {
        processedCharts[key] = this.processChartData(chartData, deviceType);
      }

      return {
        overview: processedOverview,
        charts: processedCharts,
        module,
        deviceType,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error(`❌ 获取${module}模块数据失败:`, error);
      throw error;
    }
  },

  // 获取设备特定的布局配置
  getDeviceLayout(deviceType) {
    const layouts = {
      wall: {
        containerClass: 'wall-container',
        cardClass: 'wall-card',
        chartClass: 'wall-chart',
        gridCols: 4,
        chartHeight: '400px'
      },
      pc: {
        containerClass: 'pc-container',
        cardClass: 'pc-card',
        chartClass: 'pc-chart',
        gridCols: 3,
        chartHeight: '300px'
      },
      mobile: {
        containerClass: 'mobile-container',
        cardClass: 'mobile-card',
        chartClass: 'mobile-chart',
        gridCols: 2,
        chartHeight: '250px'
      }
    };
    return layouts[deviceType] || layouts.pc;
  },

  // 生成页面标题
  generatePageTitle(module, deviceType) {
    const moduleNames = {
      processing: '加工增值',
      transport: '交通工具'
    };
    
    const deviceNames = {
      wall: '大屏',
      pc: 'PC端',
      mobile: '移动端'
    };
    
    return `海南海关领导视窗 - ${moduleNames[module] || module} - ${deviceNames[deviceType] || deviceType}`;
  },

  // 获取页面元数据
  getPageMetadata(module, deviceType) {
    return {
      title: this.generatePageTitle(module, deviceType),
      layout: this.getDeviceLayout(deviceType),
      module,
      deviceType,
      lastUpdate: new Date().toLocaleString('zh-CN')
    };
  }
};

// 导出中间件
if (typeof window !== 'undefined') {
  window.DataMiddleware = DataMiddleware;
}

console.log('✅ 数据中间件初始化完成');

// ==================== 图表中间件层 ====================
const ChartMiddleware = {
  createChart(chartData, deviceType = 'pc') {
    if (!chartData) return null;

    const config = {
      title: {
        text: chartData.title || '图表',
        left: 'center',
        textStyle: {
          color: deviceType === 'wall' ? '#00d4ff' : '#1e293b',
          fontSize: deviceType === 'wall' ? 24 : 18,
          fontWeight: 'bold'
        }
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderColor: '#3b82f6',
        borderWidth: 1,
        textStyle: { color: '#333' }
      },
      grid: {
        left: '10%',
        right: '10%',
        top: '15%',
        bottom: '15%',
        containLabel: true
      }
    };

    if (chartData.type === 'pie' || chartData.type === 'doughnut') {
      config.tooltip = { trigger: 'item', formatter: '{a} <br/>{b}: {c} ({d}%)' };
      config.series = [{
        type: 'pie',
        radius: chartData.type === 'doughnut' ? ['50%', '80%'] : ['40%', '70%'],
        center: ['50%', '60%'],
        data: chartData.data || [],
        label: { show: true, formatter: '{b}: {d}%' }
      }];
    } else if (chartData.type === 'bar' || chartData.type === 'horizontalBar') {
      const isHorizontal = chartData.type === 'horizontalBar';
      config.xAxis = {
        type: isHorizontal ? 'value' : 'category',
        data: isHorizontal ? undefined : (chartData.data ? chartData.data.map(item => item.name) : [])
      };
      config.yAxis = {
        type: isHorizontal ? 'category' : 'value',
        data: isHorizontal ? (chartData.data ? chartData.data.map(item => item.name) : []) : undefined
      };
      config.series = [{
        type: 'bar',
        data: chartData.data ? chartData.data.map(item => item.value || item.amount) : []
      }];
    } else {
      config.xAxis = { type: 'category', data: chartData.months || chartData.categories || [] };
      config.yAxis = { type: 'value' };
      config.series = (chartData.series || [chartData]).map(item => ({
        name: item.name || '数据',
        type: chartData.type === 'area' ? 'line' : (chartData.type || 'line'),
        data: item.data || [],
        smooth: true,
        areaStyle: chartData.type === 'area' ? { opacity: 0.3 } : undefined
      }));
    }

    return config;
  }
};

if (typeof window !== 'undefined') {
  window.ChartMiddleware = ChartMiddleware;
}

console.log('✅ 图表中间件初始化完成');
