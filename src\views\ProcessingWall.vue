<template>
  <div class="processing-wall">
    <header class="wall-header">
      <div class="header-bg"></div>
      <div class="header-content">
        <h1 class="header-title">海南海关加工增值业务领导视窗</h1>
        <div class="header-time">{{ currentTime }}</div>
      </div>
    </header>

    <main class="wall-main">
      <!-- 核心指标区域 -->
      <section class="metrics-section">
        <div class="metrics-grid">
          <DataCard
            v-for="card in overviewCards"
            :key="card.key"
            :title="card.title"
            :value="card.value"
            :unit="card.unit"
            :growth="card.growth"
            :loading="loading"
            device-type="wall"
          />
        </div>
      </section>

      <!-- 主要图表区域 -->
      <section class="main-charts">
        <div class="chart-primary">
          <ChartContainer
            :config="monthlyTrendChart"
            :loading="loading"
            :error="error"
            device-type="wall"
            height="600px"
            @retry="loadData"
          />
        </div>
        
        <div class="chart-secondary">
          <div class="chart-item">
            <ChartContainer
              :config="companyTypesChart"
              :loading="loading"
              :error="error"
              device-type="wall"
              height="280px"
              @retry="loadData"
            />
          </div>
          <div class="chart-item">
            <ChartContainer
              :config="regionDistributionChart"
              :loading="loading"
              :error="error"
              device-type="wall"
              height="280px"
              @retry="loadData"
            />
          </div>
        </div>
      </section>

      <!-- 排行榜区域 -->
      <section class="ranking-section">
        <div class="ranking-left">
          <ChartContainer
            :config="topCompaniesChart"
            :loading="loading"
            :error="error"
            device-type="wall"
            height="400px"
            @retry="loadData"
          />
        </div>
        
        <div class="ranking-center">
          <ChartContainer
            :config="topProductsChart"
            :loading="loading"
            :error="error"
            device-type="wall"
            height="400px"
            @retry="loadData"
          />
        </div>
        
        <div class="ranking-right">
          <ChartContainer
            :config="domesticSalesChart"
            :loading="loading"
            :error="error"
            device-type="wall"
            height="400px"
            @retry="loadData"
          />
        </div>
      </section>
    </main>
  </div>
</template>

<script>
import DataCard from '../components/DataCard.vue';
import ChartContainer from '../components/ChartContainer.vue';
import chartMiddleware from '../middleware/chartMiddleware.js';

export default {
  name: 'ProcessingWall',
  components: {
    DataCard,
    ChartContainer
  },
  data() {
    return {
      loading: true,
      error: '',
      currentTime: '',
      timeInterval: null,
      overviewData: null,
      monthlyTrendChart: {},
      companyTypesChart: {},
      regionDistributionChart: {},
      topCompaniesChart: {},
      topProductsChart: {},
      domesticSalesChart: {}
    };
  },
  computed: {
    overviewCards() {
      if (!this.overviewData) return [];
      
      return [
        {
          key: 'totalValue',
          title: this.overviewData.totalValue.title,
          value: this.overviewData.totalValue.value,
          unit: this.overviewData.totalValue.unit,
          growth: this.overviewData.totalValue.growth
        },
        {
          key: 'totalVolume',
          title: this.overviewData.totalVolume.title,
          value: this.overviewData.totalVolume.value,
          unit: this.overviewData.totalVolume.unit,
          growth: this.overviewData.totalVolume.growth
        },
        {
          key: 'exemptTax',
          title: this.overviewData.exemptTax.title,
          value: this.overviewData.exemptTax.value,
          unit: this.overviewData.exemptTax.unit,
          growth: this.overviewData.exemptTax.growth
        },
        {
          key: 'registeredCompanies',
          title: this.overviewData.registeredCompanies.title,
          value: this.overviewData.registeredCompanies.value,
          unit: this.overviewData.registeredCompanies.unit,
          growth: this.overviewData.registeredCompanies.growth
        }
      ];
    }
  },
  async mounted() {
    this.updateTime();
    this.timeInterval = setInterval(this.updateTime, 1000);
    await this.loadData();
  },
  beforeUnmount() {
    if (this.timeInterval) {
      clearInterval(this.timeInterval);
    }
  },
  methods: {
    updateTime() {
      const now = new Date();
      this.currentTime = now.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    },
    
    async loadData() {
      this.loading = true;
      this.error = '';
      
      try {
        // 获取概览数据
        this.overviewData = await chartMiddleware.getOverviewData('processing');
        
        // 获取图表数据
        const [
          monthlyTrend, 
          companyTypes, 
          regionDistribution, 
          topCompanies, 
          topProducts, 
          domesticSales
        ] = await Promise.all([
          chartMiddleware.getProcessingChartData('monthlyTrend', 'wall'),
          chartMiddleware.getProcessingChartData('companyTypes', 'wall'),
          chartMiddleware.getProcessingChartData('regionDistribution', 'wall'),
          chartMiddleware.getProcessingChartData('topCompanies', 'wall'),
          chartMiddleware.getProcessingChartData('topProducts', 'wall'),
          chartMiddleware.getProcessingChartData('domesticSales', 'wall')
        ]);
        
        this.monthlyTrendChart = monthlyTrend;
        this.companyTypesChart = companyTypes;
        this.regionDistributionChart = regionDistribution;
        this.topCompaniesChart = topCompanies;
        this.topProductsChart = topProducts;
        this.domesticSalesChart = domesticSales;
        
      } catch (error) {
        console.error('加载数据失败:', error);
        this.error = '数据加载失败，请重试';
      } finally {
        this.loading = false;
      }
    }
  }
};
</script>

<style scoped>
.processing-wall {
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #0c1426 0%, #1a2332 50%, #2d3748 100%);
  overflow: hidden;
  color: white;
}

.wall-header {
  position: relative;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #1a5276 0%, #2874a6 100%);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.header-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

.header-content {
  position: relative;
  z-index: 1;
  text-align: center;
}

.header-title {
  font-size: 42px;
  font-weight: bold;
  margin: 0 0 8px 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.header-time {
  font-size: 18px;
  opacity: 0.9;
  font-family: 'Courier New', monospace;
}

.wall-main {
  height: calc(100vh - 120px);
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.metrics-section {
  flex-shrink: 0;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
}

.main-charts {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 20px;
  flex: 1;
  min-height: 0;
}

.chart-primary {
  height: 100%;
}

.chart-secondary {
  display: flex;
  flex-direction: column;
  gap: 20px;
  height: 100%;
}

.chart-secondary .chart-item {
  flex: 1;
  min-height: 0;
}

.ranking-section {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 20px;
  flex-shrink: 0;
}

.ranking-left,
.ranking-center,
.ranking-right {
  height: 100%;
}

/* 大屏特效 */
.processing-wall::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 20% 80%, rgba(26, 82, 118, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 80% 20%, rgba(40, 116, 166, 0.1) 0%, transparent 50%);
  pointer-events: none;
  z-index: 0;
}

.wall-main > * {
  position: relative;
  z-index: 1;
}

/* 响应式调整 */
@media (max-width: 1920px) {
  .header-title {
    font-size: 36px;
  }
  
  .metrics-grid {
    gap: 16px;
  }
  
  .main-charts,
  .ranking-section {
    gap: 16px;
  }
}

@media (max-width: 1600px) {
  .header-title {
    font-size: 32px;
  }
  
  .wall-main {
    padding: 16px;
    gap: 16px;
  }
}
</style>
