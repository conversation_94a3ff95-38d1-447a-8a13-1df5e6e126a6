const http = require('http');
const fs = require('fs');
const path = require('path');

const PORT = 8080;

console.log('🚀 启动海南海关领导视窗系统...');
console.log('📁 工作目录:', __dirname);

const mimeTypes = {
  '.html': 'text/html; charset=utf-8',
  '.js': 'text/javascript; charset=utf-8',
  '.css': 'text/css; charset=utf-8',
  '.json': 'application/json; charset=utf-8'
};

function getMimeType(filePath) {
  const ext = path.extname(filePath).toLowerCase();
  return mimeTypes[ext] || 'text/plain; charset=utf-8';
}

const server = http.createServer((req, res) => {
  const now = new Date().toLocaleTimeString('zh-CN', { hour12: false });
  console.log(`${now} GET ${req.url}`);

  // 路由解析
  const urlPath = req.url === '/' ? '/processing/pc' : req.url;
  const pathParts = urlPath.split('/').filter(p => p);
  
  if (pathParts.length >= 2) {
    const module = pathParts[0]; // processing 或 transport
    const device = pathParts[1]; // pc, mobile, wall
    
    // 返回HTML页面
    const htmlPath = path.join(__dirname, 'index.html');
    if (fs.existsSync(htmlPath)) {
      const html = fs.readFileSync(htmlPath, 'utf8');
      res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
      res.end(html);
      return;
    }
  }
  
  // 静态文件服务
  if (req.url.startsWith('/src/')) {
    const filePath = path.join(__dirname, req.url);
    console.log('📁 访问文件:', filePath);
    
    if (fs.existsSync(filePath)) {
      const content = fs.readFileSync(filePath, 'utf8');
      const mimeType = getMimeType(filePath);
      res.writeHead(200, { 'Content-Type': mimeType });
      res.end(content);
      console.log('✅ 文件服务:', req.url);
      return;
    }
  }
  
  // 404
  res.writeHead(404, { 'Content-Type': 'text/plain; charset=utf-8' });
  res.end('404 Not Found');
});

server.listen(PORT, () => {
  console.log(`🚀 开发服务器启动成功: http://localhost:${PORT}`);
  console.log(`📱 移动端访问: http://localhost:${PORT}/processing/mobile `);
  console.log(`💻 PC端访问: http://localhost:${PORT}/processing/pc       `);
  console.log(`🖥️  加工增值大屏: http://localhost:${PORT}/processing/wall`);
  console.log(`🚛 交通工具大屏: http://localhost:${PORT}/transport/wall  `);
});
