<template>
  <div class="data-card" :class="[`data-card--${deviceType}`, { 'data-card--loading': loading }]">
    <div class="data-card__header">
      <h3 class="data-card__title">{{ title }}</h3>
      <div v-if="growth !== undefined" class="data-card__growth" :class="growthClass">
        <span class="growth-icon">{{ growth >= 0 ? '↗' : '↘' }}</span>
        <span class="growth-text">{{ Math.abs(growth) }}%</span>
      </div>
    </div>
    
    <div class="data-card__content">
      <div class="data-card__value">
        <span class="value-number">{{ formattedValue }}</span>
        <span class="value-unit">{{ unit }}</span>
      </div>
      
      <div v-if="subtitle" class="data-card__subtitle">
        {{ subtitle }}
      </div>
    </div>
    
    <div v-if="loading" class="data-card__loading">
      <div class="loading-spinner"></div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DataCard',
  props: {
    title: {
      type: String,
      required: true
    },
    value: {
      type: [Number, String],
      required: true
    },
    unit: {
      type: String,
      default: ''
    },
    growth: {
      type: Number,
      default: undefined
    },
    subtitle: {
      type: String,
      default: ''
    },
    deviceType: {
      type: String,
      default: 'pc',
      validator: value => ['mobile', 'pc', 'wall'].includes(value)
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    formattedValue() {
      if (typeof this.value === 'number') {
        if (this.value >= 10000) {
          return (this.value / 10000).toFixed(1);
        }
        return this.value.toLocaleString();
      }
      return this.value;
    },
    growthClass() {
      return {
        'growth--positive': this.growth >= 0,
        'growth--negative': this.growth < 0
      };
    }
  }
};
</script>

<style scoped>
.data-card {
  background: #ffffff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.data-card:hover {
  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.data-card--loading {
  opacity: 0.7;
}

.data-card__header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.data-card__title {
  font-size: 14px;
  color: #666;
  margin: 0;
  font-weight: 500;
  line-height: 1.4;
}

.data-card__growth {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 500;
}

.growth--positive {
  color: #52c41a;
}

.growth--negative {
  color: #f5222d;
}

.growth-icon {
  font-size: 14px;
}

.data-card__content {
  text-align: left;
}

.data-card__value {
  display: flex;
  align-items: baseline;
  gap: 4px;
  margin-bottom: 8px;
}

.value-number {
  font-size: 28px;
  font-weight: bold;
  color: #1a5276;
  line-height: 1;
}

.value-unit {
  font-size: 14px;
  color: #666;
  font-weight: normal;
}

.data-card__subtitle {
  font-size: 12px;
  color: #999;
  line-height: 1.4;
}

.data-card__loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(255, 255, 255, 0.8);
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #1a5276;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 移动端样式 */
.data-card--mobile {
  padding: 16px;
  border-radius: 6px;
}

.data-card--mobile .data-card__title {
  font-size: 13px;
}

.data-card--mobile .value-number {
  font-size: 24px;
}

.data-card--mobile .value-unit {
  font-size: 12px;
}

/* 大屏样式 */
.data-card--wall {
  padding: 30px;
  border-radius: 12px;
}

.data-card--wall .data-card__title {
  font-size: 18px;
}

.data-card--wall .value-number {
  font-size: 36px;
}

.data-card--wall .value-unit {
  font-size: 18px;
}

.data-card--wall .data-card__growth {
  font-size: 16px;
}

.data-card--wall .growth-icon {
  font-size: 18px;
}
</style>
