// 海南海关领导视窗系统 - 完整主应用逻辑
console.log('🚀 启动海南海关领导视窗系统...');

// 检查依赖
console.log('🔍 检查依赖加载状态...');
console.log('ECharts:', typeof echarts !== 'undefined' ? '✅ 已加载' : '❌ 未加载');
console.log('SimulationData:', typeof SimulationData !== 'undefined' ? '✅ 已加载' : '❌ 未加载');
console.log('ChartMiddleware:', typeof ChartMiddleware !== 'undefined' ? '✅ 已加载' : '❌ 未加载');

// 全局变量
let currentDevice = 'pc';
let currentModule = 'processing';
let charts = {};
let simulationData = null;

// 设备检测
function detectDevice() {
  const path = window.location.pathname;
  
  if (path.includes('/mobile')) {
    currentDevice = 'mobile';
  } else if (path.includes('/wall')) {
    currentDevice = 'wall';
  } else {
    currentDevice = 'pc';
  }
  
  if (path.includes('/transport')) {
    currentModule = 'transport';
  } else {
    currentModule = 'processing';
  }
  
  console.log(`📱 检测到设备类型: ${currentDevice}, 模块: ${currentModule}`);
  return { device: currentDevice, module: currentModule };
}

// 获取仿真数据
function getSimulationData() {
  if (window.SimulationData) {
    simulationData = window.SimulationData;
    console.log('✅ 仿真数据加载成功');
    return simulationData;
  } else {
    console.warn('⚠️ 仿真数据未加载，使用默认数据');
    return null;
  }
}

// 初始化应用
function initApp() {
  console.log('🔧 初始化应用...');

  try {
    const { device, module } = detectDevice();
    console.log(`📱 设备: ${device}, 模块: ${module}`);

    // 获取仿真数据
    getSimulationData();

    // 设置页面标题
    document.title = `海南海关领导视窗 - ${module === 'processing' ? '加工增值' : '交通工具'} - ${device.toUpperCase()}`;

    // 渲染对应视图
    renderView(device, module);

    // 初始化图表
    setTimeout(() => {
      console.log('⏰ 开始初始化图表...');
      initCharts(device, module);
    }, 1000);

  } catch (error) {
    console.error('❌ 应用初始化失败:', error);
    const app = document.getElementById('app');
    if (app) {
      app.innerHTML = `
        <div style="padding: 20px; background: #fee; color: #c00; margin: 20px; border-radius: 8px;">
          <h2>系统初始化失败</h2>
          <p>错误信息: ${error.message}</p>
        </div>
      `;
    }
  }
}

// 渲染视图
function renderView(device, module) {
  const app = document.getElementById('app');
  if (!app) {
    console.error('❌ 找不到app元素');
    return;
  }

  console.log(`🎨 渲染${device}视图 - ${module}模块`);

  try {
    switch (device) {
      case 'mobile':
        renderMobileView(app, module);
        break;
      case 'wall':
        renderWallView(app, module);
        break;
      default:
        renderPCView(app, module);
    }
    console.log('✅ 视图渲染完成');
  } catch (error) {
    console.error('❌ 视图渲染失败:', error);
    app.innerHTML = `
      <div style="padding: 20px; background: #fee; color: #c00; margin: 20px; border-radius: 8px;">
        <h2>视图渲染失败</h2>
        <p>设备: ${device}, 模块: ${module}</p>
        <p>错误: ${error.message}</p>
      </div>
    `;
  }
}

// PC端视图
function renderPCView(container, module) {
  container.className = 'pc-container';
  const moduleTitle = module === 'processing' ? '加工增值' : '交通工具';
  
  container.innerHTML = `
    <div class="pc-header">
      <h1>海南海关领导视窗 - ${moduleTitle}</h1>
      <div class="header-stats">
        <div class="stat-item">
          <span class="stat-label">总值</span>
          <span class="stat-value">${module === 'processing' ? '125.8亿元' : '18.9亿元'}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">企业数</span>
          <span class="stat-value">${module === 'processing' ? '1256家' : '856家'}</span>
        </div>
      </div>
    </div>
    <div class="pc-content">
      <div class="chart-grid">
        <div class="chart-card">
          <div id="chart1" class="chart-container"></div>
        </div>
        <div class="chart-card">
          <div id="chart2" class="chart-container"></div>
        </div>
        <div class="chart-card">
          <div id="chart3" class="chart-container"></div>
        </div>
        <div class="chart-card">
          <div id="chart4" class="chart-container"></div>
        </div>
      </div>
    </div>
  `;
}

// 移动端视图
function renderMobileView(container, module) {
  container.className = 'mobile-container';
  const moduleTitle = module === 'processing' ? '加工增值' : '交通工具';
  
  container.innerHTML = `
    <div class="mobile-header">
      <h2>海关领导视窗</h2>
      <p>${moduleTitle}</p>
      <div class="mobile-stats">
        <span class="mobile-stat">${module === 'processing' ? '125.8亿元' : '18.9亿元'}</span>
        <span class="mobile-stat">${module === 'processing' ? '1256家' : '856家'}</span>
      </div>
    </div>
    <div class="mobile-content">
      <div class="mobile-chart-list">
        <div class="mobile-chart-card">
          <div id="chart1" class="chart-container"></div>
        </div>
        <div class="mobile-chart-card">
          <div id="chart2" class="chart-container"></div>
        </div>
        <div class="mobile-chart-card">
          <div id="chart3" class="chart-container"></div>
        </div>
        <div class="mobile-chart-card">
          <div id="chart4" class="chart-container"></div>
        </div>
      </div>
    </div>
  `;
}

// 大屏视图
function renderWallView(container, module) {
  container.className = 'wall-container';
  const moduleTitle = module === 'processing' ? '加工增值监控大屏' : '交通工具监控大屏';
  
  container.innerHTML = `
    <div class="wall-header">
      <h1>海南海关领导视窗</h1>
      <h2>${moduleTitle}</h2>
      <div class="wall-stats">
        <div class="wall-stat-item">
          <div class="wall-stat-value">${module === 'processing' ? '125.8' : '18.9'}</div>
          <div class="wall-stat-label">总值(亿元)</div>
        </div>
        <div class="wall-stat-item">
          <div class="wall-stat-value">${module === 'processing' ? '1256' : '856'}</div>
          <div class="wall-stat-label">企业数(家)</div>
        </div>
        <div class="wall-stat-item">
          <div class="wall-stat-value">${module === 'processing' ? '45.6' : '12.3'}</div>
          <div class="wall-stat-label">减免税(亿元)</div>
        </div>
      </div>
    </div>
    <div class="wall-content">
      <div class="wall-chart-grid">
        <div class="wall-chart-large">
          <div id="chart1" class="chart-container"></div>
        </div>
        <div class="wall-chart-medium">
          <div id="chart2" class="chart-container"></div>
        </div>
        <div class="wall-chart-medium">
          <div id="chart3" class="chart-container"></div>
        </div>
        <div class="wall-chart-small">
          <div id="chart4" class="chart-container"></div>
        </div>
      </div>
    </div>
  `;
}

// 初始化图表
function initCharts(device, module) {
  console.log(`📊 初始化${device}设备的${module}模块图表...`);
  
  // 获取仿真数据
  const data = simulationData || window.SimulationData;
  
  if (!data) {
    console.warn('⚠️ 无法获取仿真数据，使用默认数据');
    initDefaultCharts(device, module);
    return;
  }
  
  // 根据模块获取对应数据
  const moduleData = data[module];
  if (!moduleData) {
    console.warn(`⚠️ 未找到${module}模块数据`);
    return;
  }
  
  // 创建图表配置
  const chartConfigs = getChartConfigs(moduleData, module);
  
  // 创建图表
  chartConfigs.forEach((config, index) => {
    const chartId = `chart${index + 1}`;
    const chartElement = document.getElementById(chartId);
    
    if (chartElement && window.ChartMiddleware) {
      try {
        const chartConfig = window.ChartMiddleware.createChart(config, device);
        if (chartConfig) {
          charts[chartId] = echarts.init(chartElement);
          charts[chartId].setOption(chartConfig);
          console.log(`✅ 图表${chartId}创建成功: ${config.title}`);
        }
      } catch (error) {
        console.error(`❌ 图表${chartId}创建失败:`, error);
      }
    }
  });
  
  // 响应式处理
  window.addEventListener('resize', () => {
    Object.values(charts).forEach(chart => {
      if (chart && chart.resize) {
        chart.resize();
      }
    });
  });
}

// 获取图表配置
function getChartConfigs(moduleData, module) {
  if (module === 'processing') {
    return [
      { ...moduleData.cargoStats, type: 'line' },
      { ...moduleData.taxExemption, type: 'pie' },
      { ...moduleData.registeredCompanies, type: 'bar' },
      { ...moduleData.materialTaxExemptionRanking, type: 'horizontalBar' }
    ];
  } else {
    return [
      { ...moduleData.zeroDutyCars, type: 'line' },
      { ...moduleData.transportTaxExemption, type: 'pie' },
      { ...moduleData.equipmentTaxExemption, type: 'bar' },
      { ...moduleData.transportTaxExemption, type: 'horizontalBar' }
    ];
  }
}

// 默认图表数据（备用）
function initDefaultCharts(device, module) {
  console.log('🔄 使用默认图表数据...');

  const defaultData = {
    processing: [
      { title: '货物总值趋势', type: 'line', monthlyTrend: [
        { month: '1月', value: 8.5 }, { month: '2月', value: 7.8 }, { month: '3月', value: 9.2 },
        { month: '4月', value: 10.1 }, { month: '5月', value: 11.5 }, { month: '6月', value: 12.3 }
      ]},
      { title: '免征税款分布', type: 'pie', categoryDistribution: [
        { name: '进口关税', value: 18.5 }, { name: '增值税', value: 15.2 },
        { name: '消费税', value: 8.9 }, { name: '其他税费', value: 3.0 }
      ]},
      { title: '备案企业统计', type: 'bar', industryDistribution: [
        { name: '制造业', value: 456 }, { name: '贸易业', value: 328 },
        { name: '服务业', value: 251 }, { name: '科技业', value: 138 }
      ]},
      { title: '企业排行榜', type: 'horizontalBar', companyRanking: [
        { name: '海南石化集团', amount: 5.8 }, { name: '三亚制药有限公司', amount: 4.2 },
        { name: '海口电子科技', amount: 3.9 }, { name: '洋浦炼化公司', amount: 3.6 }
      ]}
    ],
    transport: [
      { title: '零关税汽车趋势', type: 'line', monthlyTrend: [
        { month: '1月', count: 198 }, { month: '2月', count: 165 }, { month: '3月', count: 223 },
        { month: '4月', count: 267 }, { month: '5月', count: 289 }, { month: '6月', count: 312 }
      ]},
      { title: '汽车品牌分布', type: 'pie', brandDistribution: [
        { name: '奔驰', value: 685 }, { name: '宝马', value: 571 },
        { name: '奥迪', value: 457 }, { name: '保时捷', value: 343 }
      ]},
      { title: '减免税费统计', type: 'bar', companyRanking: [
        { name: '汽车贸易', amount: 2.8 }, { name: '游艇销售', amount: 2.1 },
        { name: '豪车进口', amount: 1.9 }, { name: '交通设备', amount: 1.6 }
      ]},
      { title: '设备企业排行', type: 'horizontalBar', companyRanking: [
        { name: '海南制造业集团', amount: 1.9 }, { name: '三亚科技园区', amount: 1.5 },
        { name: '海口工业园', amount: 1.3 }, { name: '洋浦经济开发区', amount: 1.1 }
      ]}
    ]
  };

  const chartData = defaultData[module] || defaultData.processing;

  chartData.forEach((config, index) => {
    const chartId = `chart${index + 1}`;
    const chartElement = document.getElementById(chartId);

    if (chartElement && window.ChartMiddleware) {
      try {
        const chartConfig = window.ChartMiddleware.createChart(config, device);
        if (chartConfig) {
          charts[chartId] = echarts.init(chartElement);
          charts[chartId].setOption(chartConfig);
          console.log(`✅ 默认图表${chartId}创建成功: ${config.title}`);
        }
      } catch (error) {
        console.error(`❌ 默认图表${chartId}创建失败:`, error);
      }
    }
  });
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', initApp);

console.log('✅ 完整主应用逻辑加载完成');
