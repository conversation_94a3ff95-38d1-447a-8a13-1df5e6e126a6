// 海南海关领导视窗系统 - Vue3应用入口
console.log('🚀 启动海南海关领导视窗系统...');

// 检查依赖
console.log('🔍 检查依赖加载状态...');
console.log('Vue:', typeof Vue !== 'undefined' ? '✅ 已加载' : '❌ 未加载');
console.log('ECharts:', typeof echarts !== 'undefined' ? '✅ 已加载' : '❌ 未加载');
console.log('SimulationData:', typeof SimulationData !== 'undefined' ? '✅ 已加载' : '❌ 未加载');

// 路由配置
const routes = [
  { path: '/', redirect: '/processing/wall' },
  { path: '/processing/mobile', component: 'ProcessingMobile' },
  { path: '/processing/pc', component: 'ProcessingPC' },
  { path: '/processing/wall', component: 'ProcessingWall' },
  { path: '/transport/wall', component: 'TransportWall' }
];

// 简单路由器
class SimpleRouter {
  constructor(routes) {
    this.routes = routes;
    this.currentRoute = null;
    this.init();
  }

  init() {
    window.addEventListener('popstate', () => this.handleRoute());
    this.handleRoute();
  }

  handleRoute() {
    const path = window.location.pathname;
    console.log(`🔄 路由变化: ${path}`);

    const route = this.routes.find(r => r.path === path);
    if (route) {
      if (route.redirect) {
        this.push(route.redirect);
        return;
      }
      this.currentRoute = route;
      this.renderComponent(route.component);
    } else {
      console.warn(`⚠️ 未找到路由: ${path}`);
      this.push('/processing/wall');
    }
  }

  push(path) {
    window.history.pushState({}, '', path);
    this.handleRoute();
  }

  renderComponent(componentName) {
    console.log(`🎨 渲染组件: ${componentName}`);
    const app = document.getElementById('app');

    // 清空现有内容
    app.innerHTML = '<div class="loading">系统加载中...</div>';

    // 根据组件名渲染对应视图
    switch(componentName) {
      case 'ProcessingWall':
        this.renderProcessingWall();
        break;
      case 'TransportWall':
        this.renderTransportWall();
        break;
      case 'ProcessingPC':
        this.renderProcessingPC();
        break;
      case 'ProcessingMobile':
        this.renderProcessingMobile();
        break;
      default:
        app.innerHTML = '<div class="error">未知组件</div>';
    }
  }

  // 渲染加工增值大屏
  renderProcessingWall() {
    const app = document.getElementById('app');
    app.innerHTML = `
      <div class="processing-wall">
        <header class="wall-header">
          <div class="header-bg"></div>
          <div class="header-content">
            <h1 class="header-title">海南海关领导视窗 - 加工增值</h1>
            <div class="header-stats">
              <span class="stat-item">总值: <span id="totalValue">125.8亿元</span></span>
              <span class="stat-item">总量: <span id="totalVolume">1256万</span></span>
            </div>
            <div class="header-time" id="currentTime"></div>
          </div>
        </header>

        <main class="wall-main">
          <div class="charts-grid">
            <div class="chart-card chart-large">
              <div class="chart-title">货物总值及货量月度趋势</div>
              <div class="chart-container" id="monthlyTrendChart"></div>
            </div>

            <div class="chart-card">
              <div class="chart-title">重点企业类型分布</div>
              <div class="chart-container" id="companyTypesChart"></div>
            </div>

            <div class="chart-card">
              <div class="chart-title">主要地区分布</div>
              <div class="chart-container" id="regionChart"></div>
            </div>

            <div class="chart-card">
              <div class="chart-title">加工增值产值TOP5企业</div>
              <div class="chart-container" id="topCompaniesChart"></div>
            </div>
          </div>
        </main>
      </div>
    `;

    // 初始化图表
    setTimeout(() => {
      this.initProcessingWallCharts();
      this.startTimeUpdate();
    }, 100);
  }

  // 渲染交通工具大屏
  renderTransportWall() {
    const app = document.getElementById('app');
    app.innerHTML = `
      <div class="transport-wall">
        <header class="wall-header">
          <div class="header-bg"></div>
          <div class="header-content">
            <h1 class="header-title">海南海关领导视窗 - 交通工具</h1>
            <div class="header-stats">
              <span class="stat-item">零关税进口: <span id="zeroTariffCount">2,456台</span></span>
              <span class="stat-item">免税金额: <span id="taxFreeAmount">8.9亿元</span></span>
            </div>
            <div class="header-time" id="currentTime"></div>
          </div>
        </header>

        <main class="wall-main">
          <div class="charts-grid">
            <div class="chart-card chart-large">
              <div class="chart-title">零关税进口小汽车月度统计</div>
              <div class="chart-container" id="carImportsChart"></div>
            </div>

            <div class="chart-card">
              <div class="chart-title">交通工具类型分布</div>
              <div class="chart-container" id="vehicleTypesChart"></div>
            </div>

            <div class="chart-card">
              <div class="chart-title">免税交通工具区域分布</div>
              <div class="chart-container" id="regionDistChart"></div>
            </div>

            <div class="chart-card">
              <div class="chart-title">减免税费企业排行</div>
              <div class="chart-container" id="enterpriseRankChart"></div>
            </div>
          </div>
        </main>
      </div>
    `;

    // 初始化图表
    setTimeout(() => {
      this.initTransportWallCharts();
      this.startTimeUpdate();
    }, 100);
  }

  // PC端渲染
  renderProcessingPC() {
    const app = document.getElementById('app');
    app.innerHTML = `
      <div class="processing-pc">
        <header class="pc-header">
          <h1>海南海关领导视窗 - 加工增值业务</h1>
          <div class="header-time" id="currentTime"></div>
        </header>
        <main class="pc-main">
          <div class="overview-cards">
            <div class="card">
              <div class="card-title">货物总值</div>
              <div class="card-value">125.8<span class="unit">亿元</span></div>
            </div>
            <div class="card">
              <div class="card-title">货物总量</div>
              <div class="card-value">1256<span class="unit">万</span></div>
            </div>
            <div class="card">
              <div class="card-title">免征税款</div>
              <div class="card-value">45.6<span class="unit">亿元</span></div>
            </div>
            <div class="card">
              <div class="card-title">备案企业</div>
              <div class="card-value">892<span class="unit">家</span></div>
            </div>
          </div>
          <div class="charts-container">
            <div class="chart-item">
              <div class="chart-title">月度趋势</div>
              <div class="chart-container" id="pcMonthlyChart"></div>
            </div>
            <div class="chart-item">
              <div class="chart-title">企业分布</div>
              <div class="chart-container" id="pcCompanyChart"></div>
            </div>
          </div>
        </main>
      </div>
    `;

    setTimeout(() => {
      this.initProcessingPCCharts();
      this.startTimeUpdate();
    }, 100);
  }

  // 移动端渲染
  renderProcessingMobile() {
    const app = document.getElementById('app');
    app.innerHTML = `
      <div class="processing-mobile">
        <header class="mobile-header">
          <h1>海关领导视窗</h1>
          <div class="header-time" id="currentTime"></div>
        </header>
        <main class="mobile-main">
          <div class="mobile-cards">
            <div class="mobile-card">
              <div class="card-title">货物总值</div>
              <div class="card-value">125.8亿元</div>
            </div>
            <div class="mobile-card">
              <div class="card-title">货物总量</div>
              <div class="card-value">1256万</div>
            </div>
          </div>
          <div class="mobile-chart">
            <div class="chart-title">月度趋势</div>
            <div class="chart-container" id="mobileChart"></div>
          </div>
        </main>
      </div>
    `;

    setTimeout(() => {
      this.initProcessingMobileCharts();
      this.startTimeUpdate();
    }, 100);
  }

  // 时间更新
  startTimeUpdate() {
    const updateTime = () => {
      const now = new Date();
      const timeStr = now.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
      const timeElement = document.getElementById('currentTime');
      if (timeElement) {
        timeElement.textContent = timeStr;
      }
    };

    updateTime();
    setInterval(updateTime, 1000);
  }

  // 初始化加工增值大屏图表
  initProcessingWallCharts() {
    console.log('🎨 初始化加工增值大屏图表...');

    // 月度趋势图
    const monthlyChart = echarts.init(document.getElementById('monthlyTrendChart'));
    const monthlyOption = {
      title: { show: false },
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0,0,0,0.8)',
        textStyle: { color: '#fff' }
      },
      legend: {
        data: ['货物总值', '货物总量'],
        textStyle: { color: '#fff' },
        top: 20
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
        axisLine: { lineStyle: { color: '#fff' } },
        axisLabel: { color: '#fff' }
      },
      yAxis: [
        {
          type: 'value',
          name: '货物总值(亿元)',
          nameTextStyle: { color: '#fff' },
          axisLine: { lineStyle: { color: '#fff' } },
          axisLabel: { color: '#fff' },
          splitLine: { lineStyle: { color: 'rgba(255,255,255,0.1)' } }
        },
        {
          type: 'value',
          name: '货物总量(万)',
          nameTextStyle: { color: '#fff' },
          axisLine: { lineStyle: { color: '#fff' } },
          axisLabel: { color: '#fff' },
          splitLine: { show: false }
        }
      ],
      series: [
        {
          name: '货物总值',
          type: 'line',
          data: [8.5, 7.8, 9.2, 10.1, 11.5, 12.3, 13.1, 12.8, 11.9, 10.6, 9.8, 8.2],
          smooth: true,
          lineStyle: { color: '#00d4ff', width: 3 },
          itemStyle: { color: '#00d4ff' },
          areaStyle: { color: 'rgba(0,212,255,0.1)' }
        },
        {
          name: '货物总量',
          type: 'line',
          yAxisIndex: 1,
          data: [62, 58, 68, 73, 81, 89, 94, 92, 87, 79, 74, 59],
          smooth: true,
          lineStyle: { color: '#ff6b6b', width: 3 },
          itemStyle: { color: '#ff6b6b' },
          areaStyle: { color: 'rgba(255,107,107,0.1)' }
        }
      ]
    };
    monthlyChart.setOption(monthlyOption);

    // 企业类型分布饼图
    const companyChart = echarts.init(document.getElementById('companyTypesChart'));
    const companyOption = {
      title: { show: false },
      tooltip: {
        trigger: 'item',
        backgroundColor: 'rgba(0,0,0,0.8)',
        textStyle: { color: '#fff' }
      },
      legend: {
        orient: 'vertical',
        left: 'left',
        textStyle: { color: '#fff' }
      },
      series: [
        {
          type: 'pie',
          radius: ['40%', '70%'],
          center: ['60%', '50%'],
          data: [
            { value: 335, name: '生产型企业', itemStyle: { color: '#00d4ff' } },
            { value: 234, name: '贸易型企业', itemStyle: { color: '#ff6b6b' } },
            { value: 156, name: '服务型企业', itemStyle: { color: '#4ecdc4' } },
            { value: 167, name: '其他类型', itemStyle: { color: '#ffe66d' } }
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          },
          label: {
            color: '#fff'
          }
        }
      ]
    };
    companyChart.setOption(companyOption);

    // 地区分布图
    const regionChart = echarts.init(document.getElementById('regionChart'));
    const regionOption = {
      title: { show: false },
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0,0,0,0.8)',
        textStyle: { color: '#fff' }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '10%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: ['海口', '三亚', '儋州', '琼海', '文昌'],
        axisLine: { lineStyle: { color: '#fff' } },
        axisLabel: { color: '#fff' }
      },
      yAxis: {
        type: 'value',
        axisLine: { lineStyle: { color: '#fff' } },
        axisLabel: { color: '#fff' },
        splitLine: { lineStyle: { color: 'rgba(255,255,255,0.1)' } }
      },
      series: [
        {
          type: 'bar',
          data: [320, 280, 150, 120, 80],
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#00d4ff' },
              { offset: 1, color: '#0099cc' }
            ])
          },
          barWidth: '60%'
        }
      ]
    };
    regionChart.setOption(regionOption);

    // TOP5企业排行
    const topChart = echarts.init(document.getElementById('topCompaniesChart'));
    const topOption = {
      title: { show: false },
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0,0,0,0.8)',
        textStyle: { color: '#fff' }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '10%',
        containLabel: true
      },
      xAxis: {
        type: 'value',
        axisLine: { lineStyle: { color: '#fff' } },
        axisLabel: { color: '#fff' },
        splitLine: { lineStyle: { color: 'rgba(255,255,255,0.1)' } }
      },
      yAxis: {
        type: 'category',
        data: ['企业E', '企业D', '企业C', '企业B', '企业A'],
        axisLine: { lineStyle: { color: '#fff' } },
        axisLabel: { color: '#fff' }
      },
      series: [
        {
          type: 'bar',
          data: [15.2, 18.6, 22.3, 28.9, 35.4],
          itemStyle: {
            color: new echarts.graphic.LinearGradient(1, 0, 0, 0, [
              { offset: 0, color: '#ff6b6b' },
              { offset: 1, color: '#cc5555' }
            ])
          },
          barWidth: '60%'
        }
      ]
    };
    topChart.setOption(topOption);

    // 保存图表实例
    this.charts = { monthlyChart, companyChart, regionChart, topChart };
  }

  // 初始化交通工具大屏图表
  initTransportWallCharts() {
    console.log('🎨 初始化交通工具大屏图表...');

    // 汽车进口趋势图
    const carChart = echarts.init(document.getElementById('carImportsChart'));
    const carOption = {
      title: { show: false },
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0,0,0,0.8)',
        textStyle: { color: '#fff' }
      },
      legend: {
        data: ['进口数量', '免税金额'],
        textStyle: { color: '#fff' },
        top: 20
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
        axisLine: { lineStyle: { color: '#fff' } },
        axisLabel: { color: '#fff' }
      },
      yAxis: [
        {
          type: 'value',
          name: '进口数量(台)',
          nameTextStyle: { color: '#fff' },
          axisLine: { lineStyle: { color: '#fff' } },
          axisLabel: { color: '#fff' },
          splitLine: { lineStyle: { color: 'rgba(255,255,255,0.1)' } }
        },
        {
          type: 'value',
          name: '免税金额(万元)',
          nameTextStyle: { color: '#fff' },
          axisLine: { lineStyle: { color: '#fff' } },
          axisLabel: { color: '#fff' },
          splitLine: { show: false }
        }
      ],
      series: [
        {
          name: '进口数量',
          type: 'bar',
          data: [180, 165, 220, 245, 280, 310, 335, 320, 295, 260, 225, 190],
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#4ecdc4' },
              { offset: 1, color: '#3aa39a' }
            ])
          }
        },
        {
          name: '免税金额',
          type: 'line',
          yAxisIndex: 1,
          data: [5200, 4800, 6300, 7100, 8200, 9100, 9800, 9400, 8600, 7600, 6500, 5500],
          smooth: true,
          lineStyle: { color: '#ffe66d', width: 3 },
          itemStyle: { color: '#ffe66d' }
        }
      ]
    };
    carChart.setOption(carOption);

    // 车辆类型分布
    const vehicleChart = echarts.init(document.getElementById('vehicleTypesChart'));
    const vehicleOption = {
      title: { show: false },
      tooltip: {
        trigger: 'item',
        backgroundColor: 'rgba(0,0,0,0.8)',
        textStyle: { color: '#fff' }
      },
      legend: {
        orient: 'vertical',
        left: 'left',
        textStyle: { color: '#fff' }
      },
      series: [
        {
          type: 'pie',
          radius: ['40%', '70%'],
          center: ['60%', '50%'],
          data: [
            { value: 1245, name: '小汽车', itemStyle: { color: '#4ecdc4' } },
            { value: 456, name: '客车', itemStyle: { color: '#ffe66d' } },
            { value: 234, name: '货车', itemStyle: { color: '#ff6b6b' } },
            { value: 123, name: '特种车辆', itemStyle: { color: '#00d4ff' } }
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          },
          label: {
            color: '#fff'
          }
        }
      ]
    };
    vehicleChart.setOption(vehicleOption);

    // 区域分布
    const regionDistChart = echarts.init(document.getElementById('regionDistChart'));
    const regionDistOption = {
      title: { show: false },
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0,0,0,0.8)',
        textStyle: { color: '#fff' }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '10%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: ['海口', '三亚', '儋州', '琼海', '文昌'],
        axisLine: { lineStyle: { color: '#fff' } },
        axisLabel: { color: '#fff' }
      },
      yAxis: {
        type: 'value',
        axisLine: { lineStyle: { color: '#fff' } },
        axisLabel: { color: '#fff' },
        splitLine: { lineStyle: { color: 'rgba(255,255,255,0.1)' } }
      },
      series: [
        {
          type: 'bar',
          data: [890, 650, 420, 380, 260],
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#ffe66d' },
              { offset: 1, color: '#ccb855' }
            ])
          },
          barWidth: '60%'
        }
      ]
    };
    regionDistChart.setOption(regionDistOption);

    // 企业排行
    const enterpriseChart = echarts.init(document.getElementById('enterpriseRankChart'));
    const enterpriseOption = {
      title: { show: false },
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0,0,0,0.8)',
        textStyle: { color: '#fff' }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '10%',
        containLabel: true
      },
      xAxis: {
        type: 'value',
        axisLine: { lineStyle: { color: '#fff' } },
        axisLabel: { color: '#fff' },
        splitLine: { lineStyle: { color: 'rgba(255,255,255,0.1)' } }
      },
      yAxis: {
        type: 'category',
        data: ['企业E', '企业D', '企业C', '企业B', '企业A'],
        axisLine: { lineStyle: { color: '#fff' } },
        axisLabel: { color: '#fff' }
      },
      series: [
        {
          type: 'bar',
          data: [1.2, 1.8, 2.3, 2.9, 3.4],
          itemStyle: {
            color: new echarts.graphic.LinearGradient(1, 0, 0, 0, [
              { offset: 0, color: '#4ecdc4' },
              { offset: 1, color: '#3aa39a' }
            ])
          },
          barWidth: '60%'
        }
      ]
    };
    enterpriseChart.setOption(enterpriseOption);
  }

  // 初始化PC端图表
  initProcessingPCCharts() {
    console.log('🎨 初始化PC端图表...');

    const monthlyChart = echarts.init(document.getElementById('pcMonthlyChart'));
    const monthlyOption = {
      title: { show: false },
      tooltip: { trigger: 'axis' },
      legend: { data: ['货物总值'] },
      grid: { left: '3%', right: '4%', bottom: '3%', top: '15%', containLabel: true },
      xAxis: {
        type: 'category',
        data: ['1月', '2月', '3月', '4月', '5月', '6月']
      },
      yAxis: { type: 'value' },
      series: [{
        name: '货物总值',
        type: 'line',
        data: [8.5, 7.8, 9.2, 10.1, 11.5, 12.3],
        smooth: true,
        lineStyle: { color: '#1a5276' },
        itemStyle: { color: '#1a5276' }
      }]
    };
    monthlyChart.setOption(monthlyOption);

    const companyChart = echarts.init(document.getElementById('pcCompanyChart'));
    const companyOption = {
      title: { show: false },
      tooltip: { trigger: 'item' },
      series: [{
        type: 'pie',
        radius: '60%',
        data: [
          { value: 335, name: '生产型企业' },
          { value: 234, name: '贸易型企业' },
          { value: 156, name: '服务型企业' }
        ]
      }]
    };
    companyChart.setOption(companyOption);
  }

  // 初始化移动端图表
  initProcessingMobileCharts() {
    console.log('🎨 初始化移动端图表...');

    const mobileChart = echarts.init(document.getElementById('mobileChart'));
    const mobileOption = {
      title: { show: false },
      tooltip: { trigger: 'axis' },
      grid: { left: '3%', right: '4%', bottom: '3%', top: '10%', containLabel: true },
      xAxis: {
        type: 'category',
        data: ['1月', '2月', '3月', '4月', '5月', '6月']
      },
      yAxis: { type: 'value' },
      series: [{
        name: '货物总值',
        type: 'bar',
        data: [8.5, 7.8, 9.2, 10.1, 11.5, 12.3],
        itemStyle: { color: '#1a5276' }
      }]
    };
    mobileChart.setOption(mobileOption);
  }
}

// 启动应用
console.log('🚀 启动路由系统...');
const router = new SimpleRouter(routes);

// 窗口大小变化时重新调整图表
window.addEventListener('resize', () => {
  if (router.charts) {
    Object.values(router.charts).forEach(chart => {
      if (chart && chart.resize) {
        chart.resize();
      }
    });
  }
});

console.log('✅ 海南海关领导视窗系统启动完成');
function initApp() {
  console.log('🔧 初始化应用...');

  try {
    const { device, module } = detectDevice();
    console.log(`📱 设备: ${device}, 模块: ${module}`);

    // 获取仿真数据
    getSimulationData();

    // 设置页面标题
    document.title = `海南海关领导视窗 - ${module === 'processing' ? '加工增值' : '交通工具'} - ${device.toUpperCase()}`;

    // 渲染对应视图
    renderView(device, module);

    // 初始化图表
    setTimeout(() => {
      console.log('⏰ 开始初始化图表...');
      initCharts(device, module);
    }, 1000);

  } catch (error) {
    console.error('❌ 应用初始化失败:', error);
    const app = document.getElementById('app');
    if (app) {
      app.innerHTML = `
        <div style="padding: 20px; background: #fee; color: #c00; margin: 20px; border-radius: 8px;">
          <h2>系统初始化失败</h2>
          <p>错误信息: ${error.message}</p>
        </div>
      `;
    }
  }
}

// 渲染视图
function renderView(device, module) {
  const app = document.getElementById('app');
  if (!app) {
    console.error('❌ 找不到app元素');
    return;
  }

  console.log(`🎨 渲染${device}视图 - ${module}模块`);

  try {
    switch (device) {
      case 'mobile':
        renderMobileView(app, module);
        break;
      case 'wall':
        renderWallView(app, module);
        break;
      default:
        renderPCView(app, module);
    }
    console.log('✅ 视图渲染完成');
  } catch (error) {
    console.error('❌ 视图渲染失败:', error);
    app.innerHTML = `
      <div style="padding: 20px; background: #fee; color: #c00; margin: 20px; border-radius: 8px;">
        <h2>视图渲染失败</h2>
        <p>设备: ${device}, 模块: ${module}</p>
        <p>错误: ${error.message}</p>
      </div>
    `;
  }
}

// PC端视图
function renderPCView(container, module) {
  container.className = 'pc-container';
  const moduleTitle = module === 'processing' ? '加工增值' : '交通工具';
  
  container.innerHTML = `
    <div class="pc-header">
      <h1>海南海关领导视窗 - ${moduleTitle}</h1>
      <div class="header-stats">
        <div class="stat-item">
          <span class="stat-label">总值</span>
          <span class="stat-value">${module === 'processing' ? '125.8亿元' : '18.9亿元'}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">企业数</span>
          <span class="stat-value">${module === 'processing' ? '1256家' : '856家'}</span>
        </div>
      </div>
    </div>
    <div class="pc-content">
      <div class="chart-grid">
        <div class="chart-card">
          <div id="chart1" class="chart-container"></div>
        </div>
        <div class="chart-card">
          <div id="chart2" class="chart-container"></div>
        </div>
        <div class="chart-card">
          <div id="chart3" class="chart-container"></div>
        </div>
        <div class="chart-card">
          <div id="chart4" class="chart-container"></div>
        </div>
      </div>
    </div>
  `;
}

// 移动端视图
function renderMobileView(container, module) {
  container.className = 'mobile-container';
  const moduleTitle = module === 'processing' ? '加工增值' : '交通工具';
  
  container.innerHTML = `
    <div class="mobile-header">
      <h2>海关领导视窗</h2>
      <p>${moduleTitle}</p>
      <div class="mobile-stats">
        <span class="mobile-stat">${module === 'processing' ? '125.8亿元' : '18.9亿元'}</span>
        <span class="mobile-stat">${module === 'processing' ? '1256家' : '856家'}</span>
      </div>
    </div>
    <div class="mobile-content">
      <div class="mobile-chart-list">
        <div class="mobile-chart-card">
          <div id="chart1" class="chart-container"></div>
        </div>
        <div class="mobile-chart-card">
          <div id="chart2" class="chart-container"></div>
        </div>
        <div class="mobile-chart-card">
          <div id="chart3" class="chart-container"></div>
        </div>
        <div class="mobile-chart-card">
          <div id="chart4" class="chart-container"></div>
        </div>
      </div>
    </div>
  `;
}

// 大屏视图
function renderWallView(container, module) {
  container.className = 'wall-container';
  const moduleTitle = module === 'processing' ? '加工增值监控大屏' : '交通工具监控大屏';
  
  container.innerHTML = `
    <div class="wall-header">
      <h1>海南海关领导视窗</h1>
      <h2>${moduleTitle}</h2>
      <div class="wall-stats">
        <div class="wall-stat-item">
          <div class="wall-stat-value">${module === 'processing' ? '125.8' : '18.9'}</div>
          <div class="wall-stat-label">总值(亿元)</div>
        </div>
        <div class="wall-stat-item">
          <div class="wall-stat-value">${module === 'processing' ? '1256' : '856'}</div>
          <div class="wall-stat-label">企业数(家)</div>
        </div>
        <div class="wall-stat-item">
          <div class="wall-stat-value">${module === 'processing' ? '45.6' : '12.3'}</div>
          <div class="wall-stat-label">减免税(亿元)</div>
        </div>
      </div>
    </div>
    <div class="wall-content">
      <div class="wall-chart-grid">
        <div class="wall-chart-large">
          <div id="chart1" class="chart-container"></div>
        </div>
        <div class="wall-chart-medium">
          <div id="chart2" class="chart-container"></div>
        </div>
        <div class="wall-chart-medium">
          <div id="chart3" class="chart-container"></div>
        </div>
        <div class="wall-chart-small">
          <div id="chart4" class="chart-container"></div>
        </div>
      </div>
    </div>
  `;
}

// 初始化图表
function initCharts(device, module) {
  console.log(`📊 初始化${device}设备的${module}模块图表...`);
  
  // 获取仿真数据
  const data = simulationData || window.SimulationData;
  
  if (!data) {
    console.warn('⚠️ 无法获取仿真数据，使用默认数据');
    initDefaultCharts(device, module);
    return;
  }
  
  // 根据模块获取对应数据
  const moduleData = data[module];
  if (!moduleData) {
    console.warn(`⚠️ 未找到${module}模块数据`);
    return;
  }
  
  // 创建图表配置
  const chartConfigs = getChartConfigs(moduleData, module);
  
  // 创建图表
  chartConfigs.forEach((config, index) => {
    const chartId = `chart${index + 1}`;
    const chartElement = document.getElementById(chartId);
    
    if (chartElement && window.ChartMiddleware) {
      try {
        const chartConfig = window.ChartMiddleware.createChart(config, device);
        if (chartConfig) {
          charts[chartId] = echarts.init(chartElement);
          charts[chartId].setOption(chartConfig);
          console.log(`✅ 图表${chartId}创建成功: ${config.title}`);
        }
      } catch (error) {
        console.error(`❌ 图表${chartId}创建失败:`, error);
      }
    }
  });
  
  // 响应式处理
  window.addEventListener('resize', () => {
    Object.values(charts).forEach(chart => {
      if (chart && chart.resize) {
        chart.resize();
      }
    });
  });
}

// 获取图表配置
function getChartConfigs(moduleData, module) {
  if (module === 'processing') {
    return [
      { ...moduleData.cargoStats, type: 'line' },
      { ...moduleData.taxExemption, type: 'pie' },
      { ...moduleData.registeredCompanies, type: 'bar' },
      { ...moduleData.materialTaxExemptionRanking, type: 'horizontalBar' }
    ];
  } else {
    return [
      { ...moduleData.zeroDutyCars, type: 'line' },
      { ...moduleData.transportTaxExemption, type: 'pie' },
      { ...moduleData.equipmentTaxExemption, type: 'bar' },
      { ...moduleData.transportTaxExemption, type: 'horizontalBar' }
    ];
  }
}

// 默认图表数据（备用）
function initDefaultCharts(device, module) {
  console.log('🔄 使用默认图表数据...');

  const defaultData = {
    processing: [
      { title: '货物总值趋势', type: 'line', monthlyTrend: [
        { month: '1月', value: 8.5 }, { month: '2月', value: 7.8 }, { month: '3月', value: 9.2 },
        { month: '4月', value: 10.1 }, { month: '5月', value: 11.5 }, { month: '6月', value: 12.3 }
      ]},
      { title: '免征税款分布', type: 'pie', categoryDistribution: [
        { name: '进口关税', value: 18.5 }, { name: '增值税', value: 15.2 },
        { name: '消费税', value: 8.9 }, { name: '其他税费', value: 3.0 }
      ]},
      { title: '备案企业统计', type: 'bar', industryDistribution: [
        { name: '制造业', value: 456 }, { name: '贸易业', value: 328 },
        { name: '服务业', value: 251 }, { name: '科技业', value: 138 }
      ]},
      { title: '企业排行榜', type: 'horizontalBar', companyRanking: [
        { name: '海南石化集团', amount: 5.8 }, { name: '三亚制药有限公司', amount: 4.2 },
        { name: '海口电子科技', amount: 3.9 }, { name: '洋浦炼化公司', amount: 3.6 }
      ]}
    ],
    transport: [
      { title: '零关税汽车趋势', type: 'line', monthlyTrend: [
        { month: '1月', count: 198 }, { month: '2月', count: 165 }, { month: '3月', count: 223 },
        { month: '4月', count: 267 }, { month: '5月', count: 289 }, { month: '6月', count: 312 }
      ]},
      { title: '汽车品牌分布', type: 'pie', brandDistribution: [
        { name: '奔驰', value: 685 }, { name: '宝马', value: 571 },
        { name: '奥迪', value: 457 }, { name: '保时捷', value: 343 }
      ]},
      { title: '减免税费统计', type: 'bar', companyRanking: [
        { name: '汽车贸易', amount: 2.8 }, { name: '游艇销售', amount: 2.1 },
        { name: '豪车进口', amount: 1.9 }, { name: '交通设备', amount: 1.6 }
      ]},
      { title: '设备企业排行', type: 'horizontalBar', companyRanking: [
        { name: '海南制造业集团', amount: 1.9 }, { name: '三亚科技园区', amount: 1.5 },
        { name: '海口工业园', amount: 1.3 }, { name: '洋浦经济开发区', amount: 1.1 }
      ]}
    ]
  };

  const chartData = defaultData[module] || defaultData.processing;

  chartData.forEach((config, index) => {
    const chartId = `chart${index + 1}`;
    const chartElement = document.getElementById(chartId);

    if (chartElement && window.ChartMiddleware) {
      try {
        const chartConfig = window.ChartMiddleware.createChart(config, device);
        if (chartConfig) {
          charts[chartId] = echarts.init(chartElement);
          charts[chartId].setOption(chartConfig);
          console.log(`✅ 默认图表${chartId}创建成功: ${config.title}`);
        }
      } catch (error) {
        console.error(`❌ 默认图表${chartId}创建失败:`, error);
      }
    }
  });
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', initApp);

console.log('✅ 完整主应用逻辑加载完成');
