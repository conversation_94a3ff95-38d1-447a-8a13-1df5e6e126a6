<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试页面</title>
</head>
<body>
    <div id="app">
        <h1>测试页面</h1>
        <div id="status">正在加载...</div>
    </div>
    
    <script>
        console.log('测试脚本开始执行');
        document.getElementById('status').innerHTML = '脚本执行成功！';
        
        // 测试ECharts加载
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js';
        script.onload = function() {
            console.log('ECharts加载成功');
            document.getElementById('status').innerHTML += '<br>ECharts加载成功！';
        };
        script.onerror = function() {
            console.error('ECharts加载失败');
            document.getElementById('status').innerHTML += '<br>ECharts加载失败！';
        };
        document.head.appendChild(script);
    </script>
</body>
</html>
