// 海南海关领导视窗系统 - 仿真后端服务
console.log('🔧 初始化仿真后端服务...');

// ==================== 仿真后端数据服务 ====================
const MockBackendService = {
  // 模拟网络延迟
  async simulateNetworkDelay(ms = 100) {
    return new Promise(resolve => setTimeout(resolve, ms));
  },

  // 加工增值模块API
  async getProcessingOverview() {
    await this.simulateNetworkDelay();
    return {
      success: true,
      data: [
        { title: '货物总值', value: '28,567.89', unit: '万元', growth: '18.5', icon: '💎', color: '#00d4ff' },
        { title: '免征税款', value: '4,567.23', unit: '万元', growth: '12.3', icon: '💰', color: '#ff6b35' },
        { title: '备案企业', value: '1,285', unit: '家', growth: '8.7', icon: '🏢', color: '#4ecdc4' },
        { title: '申报单数', value: '12,456', unit: '单', growth: '15.2', icon: '📋', color: '#45b7d1' },
        { title: '加工产值', value: '156.78', unit: '亿元', growth: '25.6', icon: '⚙️', color: '#96ceb4' },
        { title: 'ERP联网企业', value: '856', unit: '家', growth: '11.2', icon: '🔗', color: '#feca57' },
        { title: '区内企业', value: '567', unit: '家', growth: '9.8', icon: '🏭', color: '#ff9ff3' },
        { title: '区外企业', value: '289', unit: '家', growth: '13.4', icon: '🌐', color: '#54a0ff' }
      ]
    };
  },

  async getProcessingCargoTrend() {
    await this.simulateNetworkDelay();
    return {
      success: true,
      data: {
        title: '货物总值及货量月度趋势',
        type: 'line',
        months: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
        series: [
          { name: '货物总值(万元)', data: [21000, 23000, 25000, 27000, 28000, 28567, 29000, 30000, 31000, 32000, 33000, 34000] },
          { name: '货物量(吨)', data: [15000, 16000, 17000, 18000, 19000, 20000, 21000, 22000, 23000, 24000, 25000, 26000] }
        ]
      }
    };
  },

  async getProcessingTaxExemption() {
    await this.simulateNetworkDelay();
    return {
      success: true,
      data: {
        title: '免征税款月度趋势统计',
        type: 'area',
        months: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
        data: [3800, 4000, 4200, 4400, 4500, 4567, 4700, 4800, 4900, 5000, 5100, 5200]
      }
    };
  },

  async getProcessingEnterpriseTypes() {
    await this.simulateNetworkDelay();
    return {
      success: true,
      data: {
        title: '重点企业类型分布（企业数量）',
        type: 'doughnut',
        data: [
          { name: '电子信息制造', value: 156, percent: 18.2 },
          { name: '纺织服装', value: 134, percent: 15.7 },
          { name: '机械制造', value: 112, percent: 13.1 },
          { name: '化工医药', value: 98, percent: 11.4 },
          { name: '食品加工', value: 87, percent: 10.2 },
          { name: '其他行业', value: 269, percent: 31.4 }
        ]
      }
    };
  },

  async getProcessingRegionDistribution() {
    await this.simulateNetworkDelay();
    return {
      success: true,
      data: {
        title: '主要地区市县分布（企业数量）',
        type: 'pie',
        data: [
          { name: '海口市', value: 342, percent: 26.6 },
          { name: '三亚市', value: 256, percent: 19.9 },
          { name: '儋州市', value: 189, percent: 14.7 },
          { name: '琼海市', value: 145, percent: 11.3 },
          { name: '文昌市', value: 123, percent: 9.6 },
          { name: '其他市县', value: 230, percent: 17.9 }
        ]
      }
    };
  },

  async getProcessingTopEnterprises() {
    await this.simulateNetworkDelay();
    return {
      success: true,
      data: {
        title: '加工增值产值TOP5企业（亿元）',
        type: 'horizontalBar',
        data: [
          { name: '海南XX电子科技有限公司', value: 15.67, growth: 23.5 },
          { name: '三亚XX制造集团', value: 13.45, growth: 18.9 },
          { name: '儋州XX加工企业', value: 11.23, growth: 15.2 },
          { name: '海口XX贸易公司', value: 9.87, growth: 12.8 },
          { name: '琼海XX实业有限公司', value: 8.76, growth: 10.3 }
        ]
      }
    };
  },

  async getProcessingMainProducts() {
    await this.simulateNetworkDelay();
    return {
      success: true,
      data: {
        title: '主要加工产品TOP5排名',
        type: 'radar',
        data: [
          { name: '电子产品', value: 356, market: 85, profit: 78, growth: 92 },
          { name: '纺织品', value: 289, market: 72, profit: 65, growth: 68 },
          { name: '机械设备', value: 234, market: 68, profit: 71, growth: 75 },
          { name: '化工产品', value: 178, market: 55, profit: 62, growth: 58 },
          { name: '食品加工', value: 145, market: 48, profit: 55, growth: 52 }
        ]
      }
    };
  },

  // 交通工具模块API
  async getTransportOverview() {
    await this.simulateNetworkDelay();
    return {
      success: true,
      data: [
        { title: '零关税进口小汽车', value: '12,345', unit: '辆', growth: '22.1', icon: '🚗', color: '#00d4ff' },
        { title: '免税交通运输工具', value: '8,967', unit: '辆', growth: '16.8', icon: '🚙', color: '#ff6b35' },
        { title: '通关效率', value: '95.8', unit: '%', growth: '3.2', icon: '⚡', color: '#4ecdc4' },
        { title: '总通关量', value: '56,789', unit: '批次', growth: '19.4', icon: '📦', color: '#45b7d1' },
        { title: '零配件维修', value: '2,345', unit: '件', growth: '14.7', icon: '🔧', color: '#96ceb4' },
        { title: '游艇进口', value: '123', unit: '艘', growth: '33.3', icon: '🛥️', color: '#feca57' },
        { title: '自用生产设备', value: '456', unit: '台', growth: '28.9', icon: '🏭', color: '#ff9ff3' },
        { title: '减免税费总额', value: '234.56', unit: '万元', growth: '21.7', icon: '💰', color: '#54a0ff' }
      ]
    };
  },

  async getTransportCarImports() {
    await this.simulateNetworkDelay();
    return {
      success: true,
      data: {
        title: '零关税进口小汽车月度趋势',
        type: 'line',
        months: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
        data: [8000, 9000, 10000, 11000, 12000, 12345, 13000, 14000, 15000, 16000, 17000, 18000]
      }
    };
  },

  async getTransportDutyFreeVehicles() {
    await this.simulateNetworkDelay();
    return {
      success: true,
      data: {
        title: '封关后免税交通运输工具统计',
        type: 'area',
        months: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
        data: [6500, 7000, 7500, 8000, 8500, 8967, 9200, 9500, 9800, 10000, 10200, 10500]
      }
    };
  },

  async getTransportVehicleTypes() {
    await this.simulateNetworkDelay();
    return {
      success: true,
      data: {
        title: '交通工具类型分布统计',
        type: 'pie',
        data: [
          { name: '小汽车', value: 7456, percent: 60.4 },
          { name: '货车', value: 3089, percent: 25.0 },
          { name: '客车', value: 1234, percent: 10.0 },
          { name: '游艇', value: 566, percent: 4.6 }
        ]
      }
    };
  },

  async getTransportClearanceEfficiency() {
    await this.simulateNetworkDelay();
    return {
      success: true,
      data: {
        title: '通关效率月度统计',
        type: 'comparison',
        months: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
        series: [
          { name: '实际效率(%)', data: [92, 93, 94, 95, 96, 95.8, 96.2, 96.5, 96.8, 97.0, 97.2, 97.5] },
          { name: '目标效率(%)', data: [90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90] }
        ]
      }
    };
  },

  async getTransportTaxExemptionRanking() {
    await this.simulateNetworkDelay();
    return {
      success: true,
      data: {
        title: '交通工具减免税费企业排行',
        type: 'ranking',
        data: [
          { rank: 1, name: '海南XX汽车贸易公司', amount: 2456.78, growth: 25.6 },
          { rank: 2, name: '三亚XX进口车行', amount: 1987.65, growth: 18.9 },
          { rank: 3, name: '儋州XX物流集团', amount: 1678.43, growth: 15.2 },
          { rank: 4, name: '海口XX运输公司', amount: 1345.21, growth: 12.8 },
          { rank: 5, name: '琼海XX贸易发展公司', amount: 1123.98, growth: 10.3 }
        ]
      }
    };
  },

  async getTransportEquipmentRanking() {
    await this.simulateNetworkDelay();
    return {
      success: true,
      data: {
        title: '自用生产设备减免税费排行',
        type: 'horizontalBar',
        data: [
          { name: '精密加工设备A', amount: 890.12, category: '机械设备' },
          { name: '智能生产线B', amount: 765.43, category: '自动化设备' },
          { name: '检测仪器C', amount: 654.32, category: '检测设备' },
          { name: '包装机械D', amount: 543.21, category: '包装设备' },
          { name: '运输设备E', amount: 432.10, category: '物流设备' }
        ]
      }
    };
  }
};

// 导出服务
if (typeof window !== 'undefined') {
  window.MockBackendService = MockBackendService;
}

console.log('✅ 仿真后端服务初始化完成');
